import { Injectable, UnauthorizedException } from "@nestjs/common";
import { JwtService } from "@nestjs/jwt";
import * as CryptoJS from "crypto-js";
import { BaseRemoteService } from "src/auth/base-remote.service";
import { Payload } from "src/auth/jwt.strategy";
import { User, UserService } from "../user/user.service";
import { PRIVATE_KEY } from "./../problem/entities/constant";

@Injectable()
export class AuthService extends BaseRemoteService {
  constructor(
    private userService: UserService,
    private jwtService: JwtService,
  ) {
    super();
  }

  encryption(token: string, key: string = PRIVATE_KEY) {
    if (!token?.length) {
      return "";
    }
    return CryptoJS.AES.encrypt(
      JSON.stringify({
        appCode: "car",
        token: `Bearer ${token}`,
        timestamp: Date.now(),
      }),
      key,
    ).toString();
  }

  async isTokenValid(payload: Payload): Promise<boolean> {
    if (payload.token.length < 10) {
      return true;
    }
    const data = await this.getToken(payload.token);
    return !!data?.length;
    /* const res = await this.execute<boolean>(
      "/auth/verify_token",
      "POST",
      payload.token,
    );
    return res === true; */
  }

  async getRealToken(secretKey: string) {
    return await this.execute<string>("/auth/real_token", "POST", null, {
      secretKey: decodeURIComponent(secretKey),
    });
  }

  async getToken(authKey: string) {
    return await this.execute<string>("/auth/get_token", "GET", authKey);
  }

  async login(token: string): Promise<{ token: string; user: Partial<User> }> {
    let _token = token;
    if (_token?.length > 2) {
      _token = await this.getToken(_token);
    }
    if (!_token?.length) {
      throw new UnauthorizedException();
    }
    const user = await this.userService.findByToken(_token);
    if (!user) {
      throw new UnauthorizedException();
    }
    const userPayload = {
      id: user.id,
      name: user.name,
      username: user.username,
      avatar: !user.avatar?.length ? undefined : user.avatar,
      lang: user.languageCode,
      roleId: user.roleId,
    };
    return {
      token: this.jwtService.sign(
        {
          token,
          id: user.id,
          name: user.name,
        },
        token.length <= 2 ? { expiresIn: "10y" } : {},
      ),
      user: userPayload,
    };
  }

  async logout(payload: Payload) {
    return await this.execute<string>("/auth/logout", "GET", payload.token);
  }

  async changeLang(payload: Payload, code: string) {
    if (payload.token.length < 10) {
      return true;
    }
    return await this.execute<string>(
      "/sys_staff/update_lang_code",
      "PUT",
      payload.token,
      { code },
    );
  }
}
