import {
  Body,
  Controller,
  Get,
  Headers,
  Param,
  Post,
  Req,
} from "@nestjs/common";
import { UserService } from "./../user/user.service";
import { AuthService } from "./auth.service";
import type { Payload } from "./jwt.strategy";
import { Public } from "./public.decorator";

@Controller("auth")
export class AuthController {
  constructor(
    private authService: AuthService,
    private userService: UserService,
  ) {}

  @Public()
  @Get("token")
  async getToken(@Headers("auth_key") authKey: string): Promise<boolean> {
    const token = await this.authService.getToken(authKey);
    return !!token?.length;
  }

  @Get("logout")
  async logout(@Req() req: { payload: Payload }): Promise<any> {
    return await this.authService.logout(req.payload);
  }

  @Public()
  @Post("login")
  async login(@Body() req: { token?: string }): Promise<any> {
    return await this.authService.login(req.token);
  }

  @Get("profile")
  getProfile(@Req() req: { payload: Payload }) {
    return this.userService.findByToken(req.payload.token);
  }

  @Get("permissions")
  getPermissions(@Req() req: { payload: Payload }) {
    return this.userService.getPermissions(req.payload.token);
  }

  @Public()
  @Get("langs")
  async getLangs(): Promise<any> {
    return await this.userService.getLanguages();
  }

  @Post("lang/:code")
  changeLang(@Req() req: { payload: Payload }, @Param("code") code: string) {
    return this.authService.changeLang(req.payload, code);
  }
}
