## 系统要求
### node版本 18
#### 打包
pnpm
```
pnpm install
pnpm build
pnpm pkg:linux
```
yarn
```
yarn install
yarn build
yarn pkg:linux
```
npm
```
npm install
npm run build
npm run pkg:linux
```
dist中为build产物，可通过node运行，target中为构建的可执行文件，可以直接执行，生产环境建议使用

## 命令行参数
path=配置文件路径

`./api_linux path=~/config.yaml`

2.配置文件格式说明
```
#数据源
datasource:
  #数据库类型
  - mysql:
      host: localhost
      port: 3306
      username: xxx
      password: xxx
      database: xxx
      	#日志 
      logging: ["query", "error", "schema"]
      	#是否自动建表, 生产环境不要配置
      synchronize: true

#文件存储路径 可选 默认值 /tmp
storage:
  base:
    dir: /tmp

#API服务器配置
server:
  url: http://*************:3000
  contextPath: admin-api
  #登录页面
  loginPage: http://xxx.xx.com/login
  #CAR系统根地址
  carUrl: http://xxx.xx.com/

#CAR系统端口 可选 默认值 3000
port: 3000
#CAR系统上下文路径 可选 默认值 /api
contextPath: /api

#邮箱配置
mail:
  transport:
    host: xxxx.xx.com
    #true or false
    secure: true
    port: 465
    #true or false
    logger: true
    auth:
      user: <EMAIL>
      pass: xxxxxx
  defaults:
     #发件人别名 可选 
    from: '"No Reply" <<EMAIL>>'
```
