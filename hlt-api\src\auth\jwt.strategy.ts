import { Injectable, UnauthorizedException } from "@nestjs/common";
import { PassportStrategy } from "@nestjs/passport";
import { ExtractJwt, Strategy } from "passport-jwt";
import { config } from "./../utils/properties";
import { AuthService } from "./auth.service";

export type Payload = {
  token: string;
  id: number;
  name: string;
};
@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
  constructor(private authService: AuthService) {
    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      secretOrKey: config.jwtSecret,
    });
  }

  async validate(payload: Payload): Promise<Payload> {
    const valid = await this.authService.isTokenValid(payload);
    if (!valid) {
      throw new UnauthorizedException();
    }
    return { ...payload };
  }
}
