import fetch, { Headers } from "node-fetch";
import { config } from "src/utils/properties";

export abstract class BaseRemoteService {
  async execute<T>(
    api: string,
    method: string,
    token?: string,
    body?: Record<string, any>,
    header?: Record<string, string>,
  ) {
    const myHeaders = new Headers();
    if (!!token?.length) {
      myHeaders.append("Authorization", `Bearer ${token}`);
    }
    if (!!header) {
      for (const key of Object.keys(header)) {
        myHeaders.append(key, header[key]);
      }
    }
    const option: Record<string, any> = {
      method,
      headers: myHeaders,
      redirect: "follow",
    };
    if (!!body) {
      myHeaders.append("Content-Type", "application/json");
      option.body = JSON.stringify(body);
    }
    const res = await fetch(`${config.baseApi}${api}`, option as any);
    const { success, data } = await res.json();
    return success ? (data as T) : null;
  }

  async executeExt<T>(
    api: string,
    method: string,
    extHost: string,
    token?: string,
    body?: Record<string, any>,
    header?: Record<string, string>,
  ) {
    const myHeaders = new Headers();
    if (!!token?.length) {
      myHeaders.append("Authorization", `Bearer ${token}`);
    }
    if (!!header) {
      for (const key of Object.keys(header)) {
        myHeaders.append(key, header[key]);
      }
    }
    const option: Record<string, any> = {
      method,
      headers: myHeaders,
      redirect: "follow",
    };
    if (!!body) {
      myHeaders.append("Content-Type", "application/json");
      option.body = JSON.stringify(body);
    }
    const res = await fetch(`${extHost}${api}`, option as any);
    return await res.json() as T;
  }
}
