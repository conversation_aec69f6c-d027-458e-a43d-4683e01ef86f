import { ModuleMetadata, Type } from "@nestjs/common";
import { PuppeteerNodeLaunchOptions } from "puppeteer-core";

export type PuppeteerModuleOptions = {
  name?: string;
  isGlobal?: boolean;
} & Partial<PuppeteerNodeLaunchOptions>;

export interface PuppeteerOptionsFactory {
  createPuppeteerOptions(
    browserName?: string,
  ): Promise<PuppeteerModuleOptions> | PuppeteerModuleOptions;
}

export interface PuppeteerModuleAsyncOptions
  extends Pick<ModuleMetadata, "imports"> {
  name?: string;
  isGlobal?: boolean;
  useExisting?: Type<PuppeteerOptionsFactory>;
  useClass?: Type<PuppeteerOptionsFactory>;
  useFactory?: (
    ...args: any[]
  ) => Promise<PuppeteerModuleOptions> | PuppeteerModuleOptions;
  inject?: any[];
}
