import { config } from "./../../../utils/properties";
import {
  ArgumentsHost,
  Catch,
  ExceptionFilter,
  HttpException,
  HttpStatus,
  Logger,
} from "@nestjs/common";
import { HttpAdapterHost } from "@nestjs/core";

@Catch(HttpException)
export class HttpExceptionFilter implements ExceptionFilter {
  constructor(private readonly httpAdapterHost: HttpAdapterHost) {}

  catch(exception: HttpException, host: ArgumentsHost) {
    const { httpAdapter } = this.httpAdapterHost;
    const ctx = host.switchToHttp();
    Logger.error(exception.message, exception);
    const status =
      exception instanceof HttpException
        ? exception.getStatus()
        : HttpStatus.INTERNAL_SERVER_ERROR;
    if (status === HttpStatus.UNAUTHORIZED) {
      httpAdapter.reply(
        ctx.getResponse(),
        {
          statusCode: status,
          timestamp: new Date().toISOString(),
          path: httpAdapter.getRequestUrl(ctx.getRequest()),
          message: "请重新登录",
          redirectUrl: config.loginPage,
          code: 1,
        },
        HttpStatus.CREATED,
      );
    } else {
      httpAdapter.reply(
        ctx.getResponse(),
        {
          statusCode: status,
          timestamp: new Date().toISOString(),
          path: httpAdapter.getRequestUrl(ctx.getRequest()),
          message: exception.message ?? "请求失败",
          code: 1,
        },
        HttpStatus.CREATED,
      );
    }
  }
}
