import { Injectable } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { BaseService } from "src/framework/service/base.service";
import { DataSource, Repository } from "typeorm";
import { Reason } from "./entities/reason.entity";
import { NodeState } from "./entities/constant";

@Injectable()
export class ReasonService extends BaseService<Reason> {
  constructor(
    @InjectRepository(Reason) repository: Repository<Reason>,
    dataSource: DataSource,
  ) {
    super(repository, dataSource);
  }

  async canFinish(reason: Reason) {
    const unComplete = await this.repository
      .createQueryBuilder("r")
      .where("r.problem.id = :pid", { pid: reason.problem.id })
      .andWhere("r.delete is false")
      .andWhere("r.state != :state", { state: NodeState.COMPLETE })
      .getCount();
    return unComplete === 0;
  }
  findByProblemId(
    id: number,
    relations: string[] = ["configs", "details"],
  ): Promise<Reason[]> {
    return this.repository.find({
      where: { problem: { id }, delete: false },
      order: { id: "ASC" },
      relations,
    });
  }

  assignSkipFields(): string[] {
    return [
      ...super.assignSkipFields(),
      "problem",
      "stateIdx",
      "details",
      "configs",
      "status",
      "state",
      "delete",
      "finishOn",
      "deleteById",
      "deleteByName",
    ];
  }
}
