import { Injectable } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { BaseService } from "src/framework/service/base.service";
import { DataSource, Repository } from "typeorm";
import { ReasonConfig } from "./entities/reason-config.entity";

@Injectable()
export class ReasonConfigService extends BaseService<ReasonConfig> {
  constructor(
    @InjectRepository(ReasonConfig) repository: Repository<ReasonConfig>,
    dataSource: DataSource,
  ) {
    super(repository, dataSource);
  }
}
