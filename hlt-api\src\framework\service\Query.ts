import {
  And,
  Between,
  FindManyOptions,
  FindOptionsRelationByString,
  FindOptionsRelations,
  In,
  <PERSON><PERSON>han,
  LessThanOrEqual,
  Like,
  <PERSON><PERSON>han,
  MoreThan<PERSON>rEqual,
  Not,
} from "typeorm";
import { FindOptionsOrder } from "typeorm/find-options/FindOptionsOrder";
import { FindOptionsWhere } from "typeorm/find-options/FindOptionsWhere";

const OperatorType = {
  LIKE: (value: string) => Like(`%${value}%`),
  NOT_LIKE: (value: string) => Not(Like(`%${value}%`)),
  START_WITH: (value: string) => Like(`${value}%`),
  NOT_START_WITH: (value: string) => Not(Like(`${value}%`)),
  END_WITH: (value: string) => Like(`%${value}`),
  NOT_END_WITH: (value: string) => Not(Like(`%${value}`)),
  IN: In,
  NIN: <T>(value: readonly T[]) => Not(In(value)),
  GT: MoreThan,
  GTE: MoreThanOrEqual,
  LT: LessThan,
  LTE: LessThanOrEqual,
  BETWEEN: <T>([from, to]: [from: T, to: T]) => Between(from, to),
  NOT_BETWEEN: <T>([from, to]: [from: T, to: T]) => Not(Between(from, to)),
  RANGE_OPEN: <T>([from, to]: [from: T, to: T]) =>
    And(MoreThan(from), LessThan(to)),
  NOT_RANGE_OPEN: <T>([from, to]: [from: T, to: T]) =>
    Not(And(MoreThan(from), LessThan(to))),
  RANGE_CLOSE: <T>([from, to]: [from: T, to: T]) =>
    And(MoreThanOrEqual(from), LessThanOrEqual(to)),
  NOT_RANGE_CLOSE: <T>([from, to]: [from: T, to: T]) =>
    Not(And(MoreThanOrEqual(from), LessThanOrEqual(to))),
  RANGE_OPEN_CLOSE: <T>([from, to]: [from: T, to: T]) =>
    And(MoreThan(from), LessThanOrEqual(to)),
  NOT_RANGE_OPEN_CLOSE: <T>([from, to]: [from: T, to: T]) =>
    Not(And(MoreThan(from), LessThanOrEqual(to))),
  RANGE_CLOSE_OPEN: <T>([from, to]: [from: T, to: T]) =>
    And(MoreThanOrEqual(from), LessThan(to)),
  NOT_RANGE_CLOSE_OPEN: <T>([from, to]: [from: T, to: T]) =>
    Not(And(MoreThanOrEqual(from), LessThan(to))),
};

export type OrderRequest = {
  property: string;
  direction?: "ASC" | "DESC";
};

export type PageRequest = {
  no?: number;
  size?: number;
  orders?: OrderRequest[];
};
export type QueryRequest = {
  params?: Record<string, any>;
  operations?: Record<string, any>;
  page?: PageRequest;
};

export class Query<T> implements FindManyOptions<T> {
  skip?: number;
  take?: number;
  order?: FindOptionsOrder<T>;
  where?: FindOptionsWhere<T>[] | FindOptionsWhere<T>;
  relations?: FindOptionsRelationByString | FindOptionsRelations<T>;
  private _isPageable = false;

  constructor(request: QueryRequest = {}) {
    const { page, operations = {}, params } = request;
    if (page) {
      const { no, size, orders } = page;
      if (typeof no !== "undefined" && typeof size !== "undefined") {
        this.skip = no * size;
        this.take = size;
        this._isPageable = true;
      }
      if (orders?.length) {
        const _order: any = {};
        for (const { property, direction = "ASC" } of orders) {
          _order[property] = direction;
        }
        this.order = _order;
      }
    }
    const where: any = {};
    const hasCondition = true; //TODO 加入handlers逻辑用户层修改条件的配置，用户层可以返回undefined,如果所有条件都是undefined hasCondition设置为false
    if (params) {
      for (const [name, value] of Object.entries(params)) {
        if (
          (Array.isArray(value) && value.length === 0) ||
          typeof value === "undefined" ||
          value === null ||
          !value.length
        ) {
          continue;
        }
        let operator: string = operations[name];
        if (!operator) {
          if (Array.isArray(value) || typeof value !== "string") {
            operator = "IN";
          } else {
            operator = "LIKE";
          }
        }
        where[name] = OperatorType[operator.toUpperCase()](value);
      }
      if (hasCondition) {
        this.where = where;
      }
    }
  }

  get pageable() {
    return this._isPageable;
  }
}

export interface BatchIdRequest {
  ids: number[];
}
