import {
  Body,
  Controller,
  ForbiddenException,
  Get,
  Param,
  Post,
  Req,
} from "@nestjs/common";
import { Payload } from "src/auth/jwt.strategy";
import { QueryRequest } from "src/framework/service/Query";
import { UserService } from "src/user/user.service";
import { MailService } from "./../mail/mail.service";
import {
  nodes,
  NodeState,
  ProblemStatus,
  ReasonStatus,
} from "./entities/constant";
import { ReasonConfig } from "./entities/reason-config.entity";
import { Reason } from "./entities/reason.entity";
import { ProblemOperateLogService } from "./problem-operate-log.service";
import { ProblemService } from "./problem.service";
import { ReasonService } from "./reason.service";

@Controller("audit")
export class AuditController {
  constructor(
    private readonly service: ProblemService,
    private readonly logService: ProblemOperateLogService,
    private readonly reasonService: ReasonService,
    private readonly mailService: MailService,
    private readonly userService: UserService,
  ) {}

  @Post("list")
  async query(@Req() req: { payload: Payload }, @Body() request: QueryRequest) {
    let status: "APPROVED" | "REJECTED" | "AUDITING" | "PROCESSING" | undefined;
    let node;
    if (!!request.params?.processStatus?.length) {
      status = request.params.processStatus[0];
      request.params.processStatus = "";
    }
    if (
      Array.isArray(request.params?.node) &&
      request.params?.node?.length > 0
    ) {
      node = [...request.params.node];
      request.params.node = [];
    }
    const { builder, pageable } = this.service.transfer(request);
    /*  if (!!node?.length) {
      builder.andWhereExists(
        builder
          .subQuery()
          .select("1")
          .from(ReasonConfig, "prc")
          .innerJoin("prc.reason", "pr")
          .where("pr.state in (:...node) and prc.ownerId = :ownerId", {
            node,
            ownerId: req.payload.id,
          })
          .andWhere("pr.delete is false")
          .andWhere(`pr.problem.id = ${builder.alias}.id`),
      );
    } */
    const subQuery = builder
      .subQuery()
      .select("1")
      .from(ReasonConfig, "prc")
      .innerJoin("prc.reason", "pr")
      .where("prc.state in (:...states) and prc.ownerId = :ownerId", {
        ownerId: req.payload.id,
        states: [
          NodeState.ANALYZE_AUDIT,
          NodeState.VALIDATE_AUDIT,
          NodeState.CQE_AUDIT,
        ],
      })
      .andWhere("pr.delete is false")
      .andWhere(`pr.problem.id = ${builder.alias}.id`)
      .andWhere("pr.stateIdx = prc.stateIdx && pr.status != :status", {
        status: ReasonStatus.REJECTED,
      });
    if (!!node?.length) {
      subQuery.andWhere("pr.state in (:...node)", { node });
    }
    /* if (status === "APPROVED") {
      subQuery.andWhere("pr.stateIdx > prc.stateIdx");
    } else if (status === "REJECTED") {
      subQuery.andWhere("pr.status = :status", {
        status: ReasonStatus.REJECTED,
      });
    } else if (status === "AUDITING") {
      subQuery.andWhere("pr.stateIdx = prc.stateIdx && pr.status != :status", {
        status: ReasonStatus.REJECTED,
      });
    } else if (status === "PROCESSING") {
      subQuery.andWhere("pr.stateIdx < prc.stateIdx && pr.status != :status", {
        status: ReasonStatus.REJECTED,
      });
    } */
    builder.andWhereExists(subQuery);
    builder.andWhere(`${builder.alias}.status not in (:...statuses)`, {
      statuses: [
        ProblemStatus.DRAFT,
        ProblemStatus.CQE,
        ProblemStatus.OBSOLETE,
        ProblemStatus.CLOSED,
      ],
    });
    builder.addOrderBy(`${builder.alias}.id`, "DESC");
    return await this.service.doQuery(builder, pageable);
  }

  hasNoPermission(reason: Reason, currentUserId: number) {
    const config = reason.configs.find(config => config.state === reason.state);
    return !config || config.ownerId !== currentUserId;
  }

  @Get(":id")
  async get(@Param("id") id: number) {
    return this.service.get(id);
  }

  @Post("submit/:id")
  async submit(
    @Req() req: { payload: Payload },
    @Param("id") id: number,
    @Body() request: { approved: boolean; remark?: string },
  ) {
    const reason = await this.reasonService.get(id, {
      problem: true,
      configs: true,
    });
    if (
      (reason.state !== NodeState.ANALYZE_AUDIT &&
        reason.state !== NodeState.VALIDATE_AUDIT &&
        reason.state !== NodeState.CQE_AUDIT) ||
      this.hasNoPermission(reason, req.payload.id)
    ) {
      throw new ForbiddenException("当前用户不能操作执行该操作");
    }
    let log;
    if (!request.approved) {
      log = this.logService.repository.create({
        problem: { id: reason.problem.id },
        operatorId: req.payload.id,
        operatorName: req.payload.name,
        action:
          reason.state === NodeState.ANALYZE_AUDIT
            ? {
                key: "problem.action.reject_analyze",
                message: `驳回了原因分析和行动计划`,
                params: { reason: request.remark },
              }
            : reason.state === NodeState.VALIDATE_AUDIT
              ? {
                  key: "problem.action.reject_validate",
                  message: `驳回了效果验证`,
                  params: { reason: request.remark },
                }
              : {
                  key: "problem.action.reject_cqe",
                  message: `驳回了最终审核`,
                  params: { reason: request.remark },
                },
      });
      reason.state = NodeState.ANALYZE;
      reason.stateIdx = 0;
      reason.status = ReasonStatus.REJECTED;
      reason.remark = request.remark;
    } else {
      const node = nodes.find(node => node.state === reason.state);
      if (!node.next) {
        throw new ForbiddenException("非法的流程节点");
      }
      log = this.logService.repository.create({
        problem: { id: reason.problem.id },
        operatorId: req.payload.id,
        operatorName: req.payload.name,
        action:
          reason.state === NodeState.ANALYZE_AUDIT
            ? {
                key: "problem.action.approve_analyze",
                message: `批准了原因分析和行动计划`,
                params: { reason: request.remark },
              }
            : reason.state === NodeState.VALIDATE_AUDIT
              ? {
                  key: "problem.action.approve_validate",
                  message: `批准了效果验证`,
                  params: { reason: request.remark },
                }
              : {
                  key: "problem.action.approve_cqe",
                  message: `批准了最终审核`,
                  params: { reason: request.remark },
                },
      });
      reason.remark = null;
      reason.state = node.next;
      reason.stateIdx = nodes.find(item => item.state === node.next).index;
      if (reason.state === NodeState.COMPLETE) {
        reason.status = ReasonStatus.CLOSED;
        reason.finishOn = new Date();
      }
    }
    const result = await this.reasonService.execute(manager => {
      manager.save(this.logService.target, log);
      return manager.save(this.reasonService.target, reason);
    });

    const problem = await this.service.get(reason.problem.id);
    if (await this.reasonService.canFinish(result)) {
      await this.service.execute(manager => {
        problem.status = ProblemStatus.CLOSED;
        return manager.save(this.service.target, problem);
      });
      const reasons = await this.reasonService.findByProblemId(
        reason.problem.id,
        ["configs", "problem"],
      );
      if (!!result.todoId) {
        await this.userService.processedTodo(req.payload.token, [
          result.todoId,
        ]);
      }
      this.mailService.sendEmail(reasons, NodeState.COMPLETE, problem);
    } else {
      if (!!result.todoId) {
        await this.userService.processedTodo(
          req.payload.token,
          [result.todoId],
          request.approved ? "1" : "2",
        );
      }
      if (reason.state !== NodeState.COMPLETE) {
        const results = await this.userService.addTodo(
          req.payload.token,
          [result],
          problem,
          result.state,
        );
        if (results?.length) {
          await this.reasonService.execute(async manager => {
            return manager.save(this.reasonService.target, results);
          });
        }
      }
      this.mailService.sendEmail([result], result.state, problem);
    }
  }

  @Get(":id/reason")
  async reasons(
    @Req() req: { payload: Payload },
    @Param("id") id: number,
  ): Promise<Reason[]> {
    const reasons = await this.reasonService.findByProblemId(id);
    return reasons.filter(
      item =>
        !item.delete &&
        item.configs.some(
          config =>
            config.ownerId === req.payload.id &&
            (config.state === NodeState.ANALYZE_AUDIT ||
              config.state === NodeState.VALIDATE_AUDIT ||
              config.state === NodeState.CQE_AUDIT),
        ),
    );
  }
}
