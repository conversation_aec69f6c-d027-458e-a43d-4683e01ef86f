import { Test, TestingModule } from "@nestjs/testing";
import { CodeRuleController } from "./code-rule.controller";
import { CodeRuleService } from "./code-rule.service";

describe("CodeRuleController", () => {
  let controller: CodeRuleController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [CodeRuleController],
      providers: [CodeRuleService],
    }).compile();

    controller = module.get<CodeRuleController>(CodeRuleController);
  });

  it("should be defined", () => {
    expect(controller).toBeDefined();
  });
});
