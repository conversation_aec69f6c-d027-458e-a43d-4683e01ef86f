import { <PERSON>, Controller, Get, Param, Post, Req } from "@nestjs/common";
import dayjs from "dayjs";
import { Payload } from "src/auth/jwt.strategy";
import { Public } from "src/auth/public.decorator";
import { QueryRequest } from "src/framework/service/Query";
import { ReasonConfig } from "src/problem/entities/reason-config.entity";
import { Equal } from "typeorm";
import {
  NodeState,
  ProblemStatus,
  ReasonStatus,
} from "./../problem/entities/constant";
import { ProblemService } from "./../problem/problem.service";
import { ReasonService } from "./../problem/reason.service";
import { DashboardService } from "./dashboard.service";

@Controller("dashboard")
export class DashboardController {
  constructor(
    private readonly dashboardService: DashboardService,
    private readonly problemService: ProblemService,
    private readonly reasonService: ReasonService,
  ) {}

  @Get("panelCount")
  @Public()
  async panel() {
    const [open, follow] = await Promise.all([
      this.problemService.repository.countBy({
        status: Equal(ProblemStatus.NEW),
      }),
      this.problemService.repository.countBy({
        status: Equal(ProblemStatus.PROCESSING),
      }),
    ]);
    return {
      open,
      follow,
    };
  }

  @Get("processing")
  async processing(@Req() req: { payload: Payload }) {
    const builder = this.problemService.repository
      .createQueryBuilder()
      .where("status not in (:...statuses)", {
        statuses: [
          ProblemStatus.OBSOLETE,
          ProblemStatus.DRAFT,
          ProblemStatus.CQE,
          ProblemStatus.CLOSED,
        ],
      });
    const subQuery = builder
      .subQuery()
      .select("1")
      .from(ReasonConfig, "prc")
      .innerJoin("prc.reason", "pr")
      .where("prc.state in (:...states) and prc.ownerId = :ownerId", {
        ownerId: req.payload.id,
        states: [
          NodeState.ANALYZE,
          NodeState.VALIDATE,
          NodeState.ANALYZE_AUDIT,
          NodeState.VALIDATE_AUDIT,
          NodeState.CQE_AUDIT,
        ],
      })
      .andWhere("pr.delete is false")
      .andWhere(`pr.problem.id = ${builder.alias}.id`)
      .andWhere("prc.stateIdx = pr.stateIdx");
    builder.andWhereExists(subQuery);
    return builder.getCount();
  }

  private async getTaskCount(req: { payload: Payload }) {
    const { builder } = this.problemService.transfer({});
    const subQuery = builder
      .subQuery()
      .select("1")
      .from(ReasonConfig, "prc")
      .innerJoin("prc.reason", "pr")
      .where("prc.state in (:...states) and prc.ownerId = :ownerId", {
        ownerId: req.payload.id,
        states: [NodeState.ANALYZE, NodeState.VALIDATE],
      })
      .andWhere("pr.delete is false")
      .andWhere(`pr.problem.id = ${builder.alias}.id`)
      .andWhere("prc.stateIdx = pr.stateIdx");
    builder.andWhereExists(subQuery);
    builder.andWhere(`${builder.alias}.status not in (:...statuses)`, {
      statuses: [
        ProblemStatus.DRAFT,
        ProblemStatus.CQE,
        ProblemStatus.OBSOLETE,
        ProblemStatus.CLOSED,
      ],
    });
    return await builder.getCount();
  }

  private async getAuditingCount(req: { payload: Payload }) {
    const { builder } = this.problemService.transfer({});
    const subQuery = builder
      .subQuery()
      .select("1")
      .from(ReasonConfig, "prc")
      .innerJoin("prc.reason", "pr")
      .where("prc.state in (:...states) and prc.ownerId = :ownerId", {
        ownerId: req.payload.id,
        states: [
          NodeState.ANALYZE_AUDIT,
          NodeState.VALIDATE_AUDIT,
          NodeState.CQE_AUDIT,
        ],
      })
      .andWhere("pr.delete is false")
      .andWhere(`pr.problem.id = ${builder.alias}.id`)
      .andWhere("pr.stateIdx = prc.stateIdx && pr.status != :status", {
        status: ReasonStatus.REJECTED,
      });
    builder.andWhereExists(subQuery);
    builder.andWhere(`${builder.alias}.status not in (:...statuses)`, {
      statuses: [
        ProblemStatus.DRAFT,
        ProblemStatus.CQE,
        ProblemStatus.OBSOLETE,
        ProblemStatus.CLOSED,
      ],
    });
    return await builder.getCount();
  }

  @Get("todo")
  async todo(@Req() req: { payload: Payload }) {
    return {
      task: await this.getTaskCount(req),
      audit: await this.getAuditingCount(req),
    };
  }

  @Get("panel/data/:status")
  @Public()
  async panelData(@Param("status") status: ProblemStatus) {
    const builder = this.problemService.repository.createQueryBuilder();
    builder.where(`${builder.alias}.status = :status`, {
      status,
    });
    return this.problemService.doQuery(builder, false);
  }

  @Get("processing/data")
  async processingData(@Req() req: { payload: Payload }) {
    const builder = this.problemService.repository.createQueryBuilder();
    builder.where(`${builder.alias}.status not in (:...statuses)`, {
      statuses: [
        ProblemStatus.OBSOLETE,
        ProblemStatus.DRAFT,
        ProblemStatus.CQE,
        ProblemStatus.CLOSED,
      ],
    });
    const subQuery = builder
      .subQuery()
      .select("1")
      .from(ReasonConfig, "prc")
      .innerJoin("prc.reason", "pr")
      .where("prc.state in (:...states) and prc.ownerId = :ownerId", {
        ownerId: req.payload.id,
        states: [
          NodeState.ANALYZE,
          NodeState.VALIDATE,
          NodeState.ANALYZE_AUDIT,
          NodeState.VALIDATE_AUDIT,
          NodeState.CQE_AUDIT,
        ],
      })
      .andWhere("pr.delete is false")
      .andWhere(`pr.problem.id = ${builder.alias}.id`)
      .andWhere("prc.stateIdx = pr.stateIdx");
    builder.andWhereExists(subQuery);
    return this.problemService.doQuery(builder, false);
  }

  getBaseBuilder(request: QueryRequest, skip = false) {
    let monthRange;
    let unqualityType;
    let category;
    let subCategory;
    if (
      Array.isArray(request.params?.monthRange) &&
      request.params?.monthRange?.length > 0
    ) {
      monthRange = [...request.params.monthRange];
      request.params.monthRange = [];
    }
    if (
      Array.isArray(request.params?.unqualityType) &&
      request.params?.unqualityType?.length > 0
    ) {
      unqualityType = request.params.unqualityType[0];
      request.params.unqualityType = [];
    }
    if (
      Array.isArray(request.params?.category) &&
      request.params?.category?.length > 0
    ) {
      category = request.params.category[0];
      request.params.category = [];
    }
    if (
      Array.isArray(request.params?.subCategory) &&
      request.params?.subCategory?.length > 0
    ) {
      subCategory = request.params.subCategory[0];
      request.params.subCategory = [];
    }
    const { builder } = this.problemService.transfer(request);
    //TODO 需要确定统计的问题状态
    builder.andWhere(`${builder.alias}.status in (:...statuses)`, {
      statuses: [
        ProblemStatus.NEW,
        ProblemStatus.PROCESSING,
        ProblemStatus.CLOSED,
      ],
    });
    if (!!monthRange?.length) {
      builder.andWhere(
        `${builder.alias}.createdOn >= :from and ${builder.alias}.createdOn <= :to`,
        {
          from: dayjs(`${monthRange[0]}`).startOf("M").toDate(),
          to: dayjs(`${monthRange[1]}`).endOf("M").toDate(),
        },
      );
    }
    if (!!unqualityType?.length) {
      if (!skip) {
        builder.leftJoin(`${builder.alias}.reasons`, "r");
      }
      builder
        .andWhere("r.delete is false")
        .andWhere("r.unqualityType = :unqualityType", { unqualityType });
    }

    if (!!subCategory?.length && !!category?.length) {
      if (!skip) {
        builder.leftJoin(`${builder.alias}.reasons`, "r");
      }
      builder
        .andWhere("r.delete is false")
        .andWhere("r.subCategory = :subCategory", { subCategory })
        .andWhere("r.category = :category", { category });
    }
    return builder;
  }

  @Post("chart/step/:pie")
  @Public()
  async stepChart(@Param("pie") pie: "1" | "0", @Body() request: QueryRequest) {
    const builder = this.getBaseBuilder(request);
    const total = pie === "1" ? await builder.getCount() : 0;
    builder
      .select(`${builder.alias}.productStep`, "type")
      .addSelect("count(1)", "value")
      .groupBy(`${builder.alias}.productStep`);
    if (pie === "0") {
      builder.addSelect(
        `DATE_FORMAT(${builder.alias}.created_on,'%Y-%m')`,
        "month",
      );
      builder.addGroupBy(`DATE_FORMAT(${builder.alias}.created_on,'%Y-%m')`);
    }
    const data = await builder.getRawMany();
    return { data, total };
  }

  @Post("chart/customer/:pie")
  @Public()
  async customerChart(
    @Param("pie") pie: "1" | "0",
    @Body() request: QueryRequest,
  ) {
    const builder = this.getBaseBuilder(request);
    const total = pie === "1" ? await builder.getCount() : 0;
    builder
      .select(`${builder.alias}.customer`, "type")
      .addSelect("count(1)", "value")
      .groupBy(`${builder.alias}.customer`);
    if (pie === "0") {
      builder.addSelect(
        `DATE_FORMAT(${builder.alias}.createdOn,'%Y-%m')`,
        "month",
      );
      builder.addGroupBy(`DATE_FORMAT(${builder.alias}.createdOn,'%Y-%m')`);
    }
    const data = await builder.getRawMany();
    return { data, total };
  }

  @Post("chart/unqualityType/:pie")
  @Public()
  async unqualityTypeChart(
    @Param("pie") pie: "1" | "0",
    @Body() request: QueryRequest,
  ) {
    const builder = this.getBaseBuilder(request);
    const total = pie === "1" ? await builder.getCount() : 0;
    builder.leftJoin(`${builder.alias}.reasons`, "r");
    builder
      .andWhere("r.delete is false")
      .andWhere("r.unqualityType is not null");
    builder
      .select("r.unqualityType", "type")
      .addSelect(`count(distinct ${builder.alias}.id)`, "value")
      .groupBy("r.unqualityType");
    if (pie === "0") {
      builder.addSelect(
        `DATE_FORMAT(${builder.alias}.created_on,'%Y-%m')`,
        "month",
      );
      builder.addGroupBy(`DATE_FORMAT(${builder.alias}.created_on,'%Y-%m')`);
    }
    const data = await builder.getRawMany();
    return { data, total };
  }
  @Post("chart/category")
  @Public()
  async reasonCategoryChart(@Body() request: QueryRequest) {
    const builder = this.getBaseBuilder(request);
    builder.leftJoin(`${builder.alias}.reasons`, "r");
    builder.andWhere("r.delete is false");
    builder
      .select("r.category", "type")
      .addSelect(`count(distinct ${builder.alias}.id)`, "value")
      .groupBy("r.category");
    const data = await builder.getRawMany();
    return { data };
  }

  @Post("chart/category/:category")
  @Public()
  async reasonSubCategoryChart(
    @Param("category") category: string,
    @Body() request: QueryRequest,
  ) {
    const builder = this.getBaseBuilder(request);
    builder.leftJoin(`${builder.alias}.reasons`, "r");
    builder.andWhere("r.delete is false");
    builder.andWhere("r.category = :category", { category });
    builder
      .select("r.subCategory", "type")
      .addSelect(`count(distinct ${builder.alias}.id)`, "value")
      .groupBy("r.subCategory");
    const data = await builder.getRawMany();
    return { data };
  }
  @Post("chart/data")
  @Public()
  async chartData(@Body() request: QueryRequest) {
    const builder = this.getBaseBuilder(request, true);
    builder.leftJoinAndSelect(
      `${builder.alias}.reasons`,
      "r",
      "r.delete is false",
    );
    builder.leftJoinAndSelect(`r.configs`, "c");
    return builder.getMany();
  }
}
