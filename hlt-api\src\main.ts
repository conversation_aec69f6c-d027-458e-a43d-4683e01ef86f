import { HttpAdapterHost, NestFactory } from "@nestjs/core";
import { AppModule } from "./app.module";
import { HttpExceptionFilter } from "./framework/filters/http-exception/http-exception.filter";
import { TransformInterceptor } from "./framework/interceptors/transform/transform.interceptor";
import { config } from "./utils/properties";

declare const module: any;

async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  app.setGlobalPrefix(config.contextPath);
  const adapterHost = app.get(HttpAdapterHost);
  app.useGlobalFilters(new HttpExceptionFilter(adapterHost));
  app.useGlobalInterceptors(new TransformInterceptor());
  await app.listen(config.port);

  if (module.hot) {
    module.hot.accept();
    module.hot.dispose(() => app.close());
  }
}

bootstrap();
