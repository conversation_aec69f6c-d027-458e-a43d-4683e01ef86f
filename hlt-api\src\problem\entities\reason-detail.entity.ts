import { Attachment, NumberIdObject } from "../../framework/model/base";
import { Column, <PERSON>tity, ManyToOne } from "typeorm";
import { Reason } from "./reason.entity";
import { ReasonDetailType } from "./constant";

@Entity()
export class ReasonDetail extends NumberIdObject {
  @ManyToOne(() => Reason, reason => reason.details, {
    nullable: true,
    onDelete: "CASCADE",
  })
  reason: Reason;
  @Column({ length: 1000, comment: "问题" })
  question: string;
  @Column({ length: 1000, comment: "答案" })
  answer: string;
  @Column({ length: 1000, comment: "证据" })
  evidence: string;
  @Column({ nullable: true, type: "json", comment: "附件" })
  attachment?: Attachment;
  @Column({
    type: "enum",
    enum: ReasonDetailType,
    default: ReasonDetailType.PRODUCE,
    comment: "原因类别",
  })
  type: ReasonDetailType;
}
