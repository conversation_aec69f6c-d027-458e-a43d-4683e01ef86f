import { Modu<PERSON> } from "@nestjs/common";
import { DictionaryService } from "./dictionary.service";
import { DictionaryController } from "./dictionary.controller";
import { TypeOrmModule } from "@nestjs/typeorm";
import { Dictionary, DictionaryRelation } from "./entities/dictionary.entity";
import { RemoteDictionaryService } from "./remote-dictionary.service";
import { DictionaryRelationService } from "./dictionary-relation.service";

@Module({
  imports: [TypeOrmModule.forFeature([Dictionary, DictionaryRelation])],
  controllers: [DictionaryController],
  providers: [
    DictionaryService,
    DictionaryRelationService,
    RemoteDictionaryService,
  ],
  exports: [DictionaryService],
})
export class DictionaryModule {}
