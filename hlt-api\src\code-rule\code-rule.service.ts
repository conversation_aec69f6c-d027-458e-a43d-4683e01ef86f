import { Injectable } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import dayjs from "dayjs";
import { DictionaryCategory } from "src/dictionary/entities/dictionary.entity";
import { Problem } from "src/problem/entities/problem.entity";
import { DataSource, Like, Not, Repository } from "typeorm";
import { FindOptionsWhere } from "typeorm/find-options/FindOptionsWhere";
import { BaseService } from "../framework/service/base.service";
import { CodeRule } from "./entities/code-rule.entity";
import { config } from "src/utils/properties";

@Injectable()
export class CodeRuleService extends BaseService<CodeRule> {
  constructor(
    @InjectRepository(CodeRule) repository: Repository<CodeRule>,
    dataSource: DataSource,
  ) {
    super(repository, dataSource);
  }

  mapper(datum: CodeRule): CodeRule {
    const { id, name } = datum.dictionary;
    return { ...datum, dictionary: { id, name } } as unknown as CodeRule;
  }

  async genCode(problem: Problem, problemRepo: Repository<Problem>) {
    const { machineType, customer, productLine, factory, businessUnit } =
      problem;
    const codeRules = await this.repository.find();
    const rules: CodeRule[] = [];
    codeRules.forEach(entity => {
      if (
        entity.option === machineType &&
        entity.dictionary.category === DictionaryCategory.MACHINE_TYPE
      ) {
        rules.push(entity);
      } else if (
        entity.option === customer &&
        entity.dictionary.category === DictionaryCategory.CUSTOMER
      ) {
        rules.push(entity);
      } else if (
        entity.option === productLine &&
        entity.dictionary.category === DictionaryCategory.PRODUCT_LINE
      ) {
        rules.push(entity);
      } else if (
        entity.option === factory &&
        entity.dictionary.category === DictionaryCategory.FACTORY
      ) {
        rules.push(entity);
      } else if (
        entity.option === businessUnit &&
        entity.dictionary.category === DictionaryCategory.BUSINESS_UNIT
      ) {
        rules.push(entity);
      }
    });
    rules.sort((r1, r2) => r1.dictionary.priority - r2.dictionary.priority);
    const prefix = rules.map(rule => rule.code).join("");
    const date = dayjs().format("YYYYMMDD");
    const fullPrefix = `${config.appName}${prefix}${date}`;
    const maxCodeProblem = await problemRepo.findOne({
      select: ["code"],
      where: { code: Like(`${fullPrefix}%`) },
      order: { code: "DESC" },
    });
    const code = !maxCodeProblem
      ? 1
      : parseInt(maxCodeProblem.code.substring(fullPrefix.length), 10) + 1;
    problem.code = `${fullPrefix}${String(code).padStart(3, "0")}`;
  }

  async duplicate(entity: Partial<CodeRule>) {
    const where: FindOptionsWhere<CodeRule> = {
      dictionary: { id: entity.dictionary.id },
      option: entity.option,
    };
    if (entity.id) {
      where.id = Not(entity.id);
    }
    return await this.repository.exist({ where });
  }
}
