import { NumberIdObject } from "../../framework/model/base";
import { Column, Entity, ManyToOne } from "typeorm";
import { Dictionary } from "../../dictionary/entities/dictionary.entity";

@Entity()
export class CodeRule extends NumberIdObject {
  @ManyToOne(() => Dictionary, { nullable: false, eager: true })
  dictionary: Dictionary;

  @Column()
  option: string;

  @Column()
  code: string;

  @Column({ nullable: true, length: 500 })
  description?: string;
}
