import { DynamicModule, Logger } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import fs from "fs";
import os from "os";
import nodePath, { join } from "path";
import propertyReader from "properties-reader";
import { type DataSourceOptions } from "typeorm";
import { SnakeNamingStrategy } from "typeorm-naming-strategies";
import { MixedList } from "typeorm/common/MixedList";
import { EntitySchema } from "typeorm/entity-schema/EntitySchema";
import YAML from "yaml";
import entities from "../config/entities";
import processArgs from "./processArgs";

const CONFIG_TYPE_ERROR_MSG =
  "缺少配置文件类型。配置文件扩展名为'yaml' | 'yml' | 'properties' 或者运行参数指定config-type='yaml' | 'yml' | 'properties'";
const CONFIG_TYPES = new Set(["yaml", "yml", "properties"]);

type Entities = MixedList<CallableFunction | string | EntitySchema>;

const findEntity = (
  _path: string,
  _entities: Record<string, string>,
  fileName?: string,
) => {
  const stats = fs.statSync(_path);
  if (stats.isDirectory()) {
    for (const file of fs.readdirSync(_path)) {
      findEntity(nodePath.join(_path, file), _entities, file);
    }
  } else {
    if (fileName) {
      if (fileName.endsWith(".entity.js") || fileName.endsWith(".entity.ts")) {
        const originName = fileName.substring(0, fileName.indexOf(".entity."));
        let name = "";
        for (const part of originName.split("-")) {
          name = `${name}${part[0].toUpperCase()}${part.substring(1)}`;
        }
        const from = `..${_path.substring(__dirname.length - "/utils".length)}`;
        _entities[name] = from.substring(0, from.lastIndexOf("."));
      }
    }
  }
};

class Config {
  private _entities: Entities;
  private _car_url: string;
  private _jwt_secret: string;
  private _jwt_expired: string | number;
  private _properties: Record<string, any>;
  private _base_storage_dir: string;
  private _server_url: string;
  private _ext_url: string;
  private _login_page: string;
  private _context_path: string;
  private _mail: Record<string, any>;
  private _test_copyers: string[];
  private _complete_cc: string[];
  private _port: number;
  private _mail_template: string;
  private _contextPath: string;
  private _app_name: string;
  private _executablePath: string;

  private loadYaml(path: string) {
    const content = fs.readFileSync(path, "utf8");
    this._properties = YAML.parse(content);
  }

  private loadProperties(path: string) {
    this._properties = propertyReader(path, "utf-8").getAllProperties();
  }

  private getPath = () => {
    const path = processArgs.path;
    if (path === undefined) {
      const yamlPath = nodePath.join(
        `${process.cwd()}`,
        process.env.APP === "DFX" ? "application_dfx.yaml" : "application.yaml",
      );
      if (fs.existsSync(yamlPath)) {
        return yamlPath;
      }
      const propertiesPath = nodePath.join(
        `${process.cwd()}`,
        "application.properties",
      );
      if (fs.existsSync(propertiesPath)) {
        return propertiesPath;
      }
      throw new Error(
        `${yamlPath} 不存在\n${propertiesPath}不存在, 未指定配置文件`,
      );
    }
    if (fs.existsSync(path)) {
      return path;
    }
    throw new Error(`${path} 不存在`);
  };

  private getType = (path: string) => {
    const type = processArgs["config.type"];
    let _type = type;
    if (type === undefined) {
      _type = nodePath.extname(path);
      if (!_type.startsWith(".")) {
        throw new Error(CONFIG_TYPE_ERROR_MSG);
      }
      _type = _type.substring(1);
    }
    return _type;
  };

  constructor() {
    const path = this.getPath();
    const type = this.getType(path);
    if (!CONFIG_TYPES.has(type)) {
      throw new Error(CONFIG_TYPE_ERROR_MSG);
    }
    if (type === "properties") {
      this.loadProperties(path);
    } else {
      this.loadYaml(path);
    }
    this._app_name = this._properties.server?.name ?? "CAR";
    this._mail_template = this._properties.server?.mailTemplate;
    this._car_url = this._properties.server?.carUrl;
    this._ext_url = this._properties.server?.extUrl;
    this._base_storage_dir = this._properties.storage?.base?.dir ?? os.tmpdir();
    this._entities = entities;
    this._jwt_secret =
      this._properties.server?.jwtSecret ??
      "5ca6a09ad1baf914af82f05af3f4edf2fb0750e102d0d3e5cc90a08ae0e35b6b";
    this._jwt_expired = this._properties.server?.jwtExpired ?? "1d";
    this._login_page = this._properties.server?.loginPage;
    this._server_url = this._properties.server?.url;
    this._context_path = this._properties.server?.contextPath;
    this._mail = { ...this._properties.mail };
    this._test_copyers = this._properties.server?.testCopyers ?? [];
    this._complete_cc = this._properties.server?.completeCopyers ?? [];
    this._port = this._properties.port ?? 3000;
    this._contextPath = this._properties.contextPath ?? "/api";
    this._executablePath = this._properties.puppeteer?.executablePath;

    if (process.env.NODE_ENV === "development") {
      const file = nodePath.resolve(
        __dirname,
        "..",
        "..",
        "src",
        "config",
        "entities.ts",
      );
      if (!fs.existsSync(file)) {
        this._entities = [__dirname + "/../**/*.entity{.ts,.js}"];
        const importInfo = {};
        findEntity(__dirname + "/..", importInfo);
        let source = "";
        for (const [name, from] of Object.entries(importInfo)) {
          source = `${source}import {${name}} from '${from}'\n`;
        }
        source = `${source}export default [${Object.keys(importInfo).join(
          ",",
        )}]`;
        fs.writeFileSync(file, source);
      }
    }
  }

  get appName() {
    return this._app_name;
  }

  get extUrl() {
    return this._ext_url;
  }

  get testCopyers() {
    return this._test_copyers;
  }

  get completeCc() {
    return this._complete_cc;
  }

  get jwtSecret() {
    return this._jwt_secret;
  }

  get jwtExpired() {
    return this._jwt_expired;
  }

  get properties() {
    return this._properties;
  }

  get carUrl() {
    return this._car_url;
  }

  get baseStoreageDir() {
    return this._base_storage_dir;
  }

  get loginPage() {
    return this._login_page;
  }

  get baseApi() {
    return `${this._server_url}/${this._context_path}`;
  }

  get baseUrl() {
    return this._server_url;
  }

  get mailTemplateDir() {
    let dir;
    if (!!this._mail_template?.length) {
      dir = join(this._mail_template, "templates");
    } else {
      dir = join(__dirname, "../mail/templates");
    }
    Logger.log("邮件模板路径配置为:" + dir);
    return dir;
  }
  get port() {
    return this._port;
  }

  get mailConfig() {
    return this._mail;
  }

  get contextPath() {
    return this._contextPath;
  }

  get executablePath() {
    return this._executablePath;
  }

  getDatasource() {
    const dsConfig: DataSourceOptions[] = this.properties?.datasource;
    const result: DynamicModule[] = [];
    if (dsConfig?.length) {
      for (const cfg of dsConfig) {
        if (dsConfig.length > 1 && cfg.name === undefined) {
          throw new Error("配置多数据源时，数据源名称不能为空");
        }
        const type = Object.keys(cfg)[0];
        result.push(
          TypeOrmModule.forRoot({
            type,
            //name: datasourceName,
            ...cfg[type],
            // entities: this._entities,
            namingStrategy: new SnakeNamingStrategy(),
            autoLoadEntities: true,
            // entities: [__dirname + "/../**/*.entity{.ts,.js}"],
            // entities: this._entities,
          }),
        );
      }
    }
    return result;
  }
}

export const config = new Config();
