import { <PERSON>du<PERSON> } from "@nestjs/common";
import { TypeOrmModule } from "@nestjs/typeorm";
import { DictionaryModule } from "src/dictionary/dictionary.module";
import { CodeRuleModule } from "./../code-rule/code-rule.module";
import { MailModule } from "./../mail/mail.module";
import { AuditController } from "./audit.controller";
import { ProblemOperateLog } from "./entities/problem-operate-log.entity";
import { Problem } from "./entities/problem.entity";
import { ReasonConfig } from "./entities/reason-config.entity";
import { ReasonDetail } from "./entities/reason-detail.entity";
import { Reason } from "./entities/reason.entity";
import { ProblemOperateLogService } from "./problem-operate-log.service";
import { ProblemController } from "./problem.controller";
import { ProblemService } from "./problem.service";
import { ReasonDetailService } from "./reason-detail.service";
import { ReasonService } from "./reason.service";
import { TaskController } from "./task.controller";
import { UserModule } from "src/user/user.module";
import { ReasonConfigService } from "./reason-config.service";

@Module({
  imports: [
    TypeOrmModule.forFeature([
      Problem,
      ProblemOperateLog,
      Reason,
      ReasonDetail,
      ReasonConfig,
    ]),
    DictionaryModule,
    CodeRuleModule,
    MailModule,
    UserModule,
  ],
  controllers: [ProblemController, TaskController, AuditController],
  providers: [
    ProblemService,
    ProblemOperateLogService,
    ReasonConfigService,
    ReasonService,
    ReasonDetailService,
  ],
  exports: [ProblemService, ReasonService],
})
export class ProblemModule {}
