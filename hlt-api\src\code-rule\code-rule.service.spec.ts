import { Test, TestingModule } from "@nestjs/testing";
import { CodeRuleService } from "./code-rule.service";

describe("CodeRuleService", () => {
  let service: CodeRuleService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [CodeRuleService],
    }).compile();

    service = module.get<CodeRuleService>(CodeRuleService);
  });

  it("should be defined", () => {
    expect(service).toBeDefined();
  });
});
