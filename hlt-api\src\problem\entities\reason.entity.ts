import { <PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, OneTo<PERSON><PERSON>, Relation } from "typeorm";
import { NumberIdTimeObject } from "../../framework/model/base";
import { NodeState, ReasonStatus } from "./constant";
import { Problem } from "./problem.entity";
import { ReasonConfig } from "./reason-config.entity";
import { ReasonDetail } from "./reason-detail.entity";

@Entity()
export class Reason extends NumberIdTimeObject {
  @ManyToOne(() => Problem, problem => problem.reasons, {
    nullable: false,
    onDelete: "CASCADE",
  })
  problem: Relation<Problem>;
  @Column({ comment: "原因类别(一级)" })
  category: string;
  @Column({ comment: "原因类别(二级)" })
  subCategory: string;
  @Column({ nullable: true, comment: "不良代码" })
  unqualityCode: string;
  @Column({ nullable: true, comment: "不良类别" })
  unqualityType: string;
  @OneToMany(() => ReasonDetail, detail => detail.reason, {
    cascade: true,
    orphanedRowAction: "delete",
  })
  details: Relation<ReasonDetail>[];
  @OneToMany(() => ReasonConfig, config => config.reason, {
    cascade: true,
    orphanedRowAction: "delete",
  })
  configs: Relation<ReasonConfig>[];
  @Column({ nullable: true, type: "text", comment: "行动计划" })
  improvement: string;
  @Column({ nullable: true, type: "text", comment: "验证结果" })
  validateResult: string;
  @Column({ nullable: true, type: "date" })
  validateOn: Date;
  @Column({ nullable: true, type: "date" })
  estimatedFinishOn: Date;
  @Column({ nullable: true, type: "date" })
  finishOn: Date;
  @Column({
    type: "enum",
    enum: ReasonStatus,
    default: ReasonStatus.OPEN,
    comment: "原因状态",
  })
  status: ReasonStatus;
  @Column({
    type: "enum",
    enum: NodeState,
    default: NodeState.ANALYZE,
    comment: "当前节点",
  })
  state: NodeState;
  @Column({ default: 0 })
  stateIdx: number;
  @Column({ default: false })
  delete: boolean;
  @Column({ nullable: true })
  deleteById: number;
  @Column({ nullable: true })
  deleteByName: string;
  @Column({ nullable: true, comment: "驳回理由", length: 500 })
  remark: string;
  @Column({ nullable: true })
  todoId: number;
}
