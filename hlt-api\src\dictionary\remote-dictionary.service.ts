import { Injectable, UnauthorizedException } from "@nestjs/common";
import { BaseRemoteService } from "src/auth/base-remote.service";

@Injectable()
export class RemoteDictionaryService extends BaseRemoteService {
  async getFactories(token?: string) {
    return await this.execute<{ label: string; value: string; id: number }[]>(
      "/sys_dict/data/list?type=factory",
      "GET",
      token,
    );
  }
}
