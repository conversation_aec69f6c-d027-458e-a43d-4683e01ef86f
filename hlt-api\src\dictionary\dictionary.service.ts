import { Injectable } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { QueryRequest } from "src/framework/service/Query";
import { DataSource, Equal, In, Repository } from "typeorm";
import { BaseService } from "../framework/service/base.service";
import { Dictionary, DictionaryCategory } from "./entities/dictionary.entity";
import { DictionaryOption } from "./types";

@Injectable()
export class DictionaryService extends BaseService<Dictionary> {
  constructor(
    @InjectRepository(Dictionary) repository: Repository<Dictionary>,
    dataSource: DataSource,
  ) {
    super(repository, dataSource);
  }

  processUpdate(source: Partial<Dictionary>, target: Dictionary) {
    target.priority = source.priority;
    target.description = source.description;
    if (target.category !== DictionaryCategory.FACTORY) {
      target.options = source.options;
    }
  }

  async query(request: QueryRequest) {
    // await this.init();
    return super.query(request);
  }

  async init() {
    const count = await this.repository.count();
    if (count === 0) {
      const initData = [
        {
          category: DictionaryCategory.CUSTOMER,
          priority: 0,
          name: "客户",
          description: "定义客户相关信息, 包括客户名称,工厂,目标值",
          options: [{ name: "客户1" }, { name: "客户2" }, { name: "客户3" }],
        },
        {
          category: DictionaryCategory.BUSINESS_UNIT,
          priority: 1,
          name: "事业部",
          description: "定义事业部相关信息",
          options: [
            { name: "BU1" },
            { name: "BU2" },
            { name: "BU3" },
            { name: "BU4" },
          ],
        },
        {
          category: DictionaryCategory.FACTORY,
          priority: 3,
          name: "和而泰工厂",
          description: "定义和而泰工厂",
          options: [
            { name: "深圳工厂" },
            { name: "越南工厂" },
            { name: "苏州工厂" },
          ],
        },
        {
          category: DictionaryCategory.PRODUCT_LINE,
          priority: 4,
          name: "生产线",
          description: "定义生产线",
          options: [{ name: "产线1" }, { name: "产线2" }, { name: "产线3" }],
        },
        {
          category: DictionaryCategory.MACHINE_TYPE,
          priority: 5,
          name: "机型",
          description: "定义机型",
          options: [{ name: "机型1" }, { name: "机型2" }, { name: "机型3" }],
        },
        {
          category: DictionaryCategory.PRODUCT_STEP,
          priority: 0,
          name: "产品阶段",
          description: "定义产品阶段",
          options: [{ name: "EVT" }, { name: "MP" }],
        },
        {
          category: DictionaryCategory.UNQUALITY_TYPE,
          priority: 0,
          name: "不良类别",
          description: "定义不良类别",
          options: [
            { name: "DFT" },
            { name: "DFM" },
            { name: "DFA" },
            { name: "DFS" },
            { name: "物料问题" },
            { name: "制程问题" },
          ],
        },
        {
          category: DictionaryCategory.REASON_TYPE,
          priority: 0,
          name: "原因类别",
          description: "定义问题原因类别",
          tree: true,
          options: [
            {
              name: "设计",
              children: [
                { name: "设计1" },
                { name: "设计2" },
                { name: "设计3" },
                { name: "设计4" },
              ],
            },
            {
              name: "制造",
              children: [
                { name: "制造1" },
                { name: "制造2" },
                { name: "制造3" },
                { name: "制造4" },
              ],
            },
            {
              name: "来料",
              children: [
                { name: "来料1" },
                { name: "来料2" },
                { name: "来料3" },
                { name: "来料4" },
              ],
            },
            {
              name: "测试",
              children: [
                { name: "测试1" },
                { name: "测试2" },
                { name: "测试3" },
                { name: "测试4" },
              ],
            },
          ],
        },
      ];
      await this.execute(manager => {
        return manager.save(initData.map(item => this.repository.create(item)));
      });
    }
  }

  async getOptions(categories: DictionaryCategory[] = []) {
    const result: Record<DictionaryCategory, DictionaryOption[]> = {} as Record<
      DictionaryCategory,
      DictionaryOption[]
    >;
    if (categories.length) {
      const dictionaries = await this.repository.findBy({
        category: In(categories),
      });
      for (const dictionary of dictionaries) {
        result[dictionary.category] = dictionary.options;
      }
    }
    return result;
  }

  async getCategoryOptions(category: DictionaryCategory) {
    const dictionary = await this.repository.findOneBy({
      category: Equal(category),
    });
    return dictionary ? dictionary.options : [];
  }

  async getDictionaryByCategory(category: DictionaryCategory) {
    return await this.repository.findOneBy({
      category: Equal(category),
    });
  }
}
