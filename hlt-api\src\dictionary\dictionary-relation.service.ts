import { Injectable } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { QueryRequest } from "src/framework/service/Query";
import { DataSource, Repository } from "typeorm";
import { BaseService } from "../framework/service/base.service";
import { DictionaryRelation } from "./entities/dictionary.entity";

@Injectable()
export class DictionaryRelationService extends BaseService<DictionaryRelation> {
  constructor(
    @InjectRepository(DictionaryRelation)
    repository: Repository<DictionaryRelation>,
    dataSource: DataSource,
  ) {
    super(repository, dataSource);
  }

  processUpdate(
    source: Partial<DictionaryRelation>,
    target: DictionaryRelation,
  ) {
    target.machineType = source.machineType;
    target.businessUnit = source.businessUnit;
    target.customer = source.customer;
  }

  async query(request: QueryRequest) {
    return super.query(request);
  }
}
