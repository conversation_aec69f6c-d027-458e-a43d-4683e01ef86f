import {
  Body,
  Controller,
  Delete,
  ForbiddenException,
  Get,
  Param,
  Post,
  Put,
  Req,
  Res,
  UploadedFile,
  UseInterceptors,
} from "@nestjs/common";
import { FileInterceptor } from "@nestjs/platform-express";
import type { Response } from "express";
import { unlinkSync } from "node:fs";
import { extname, join, parse } from "path";
import { Payload } from "src/auth/jwt.strategy";
import { Public } from "src/auth/public.decorator";
import { BatchIdRequest, QueryRequest } from "src/framework/service/Query";
import { MailService } from "src/mail/mail.service";
import { UserService } from "src/user/user.service";
import { fileNameEncodingFilter } from "src/utils/file-upload.utils";
import { Attachment } from "./../framework/model/base";
import {
  NodeState,
  ProblemStatus,
  ReasonStatus,
  nodes,
} from "./entities/constant";
import { ReasonConfig } from "./entities/reason-config.entity";
import { ReasonDetail } from "./entities/reason-detail.entity";
import { Reason } from "./entities/reason.entity";
import { ProblemOperateLogService } from "./problem-operate-log.service";
import { reasonStorage } from "./problem.controller";
import { ProblemService } from "./problem.service";
import { ReasonDetailService } from "./reason-detail.service";
import { ReasonService } from "./reason.service";

@Controller("task")
export class TaskController {
  constructor(
    private readonly service: ProblemService,
    private readonly logService: ProblemOperateLogService,
    private readonly reasonService: ReasonService,
    private readonly reasonDetailService: ReasonDetailService,
    private readonly mailService: MailService,
    private readonly userService: UserService,
  ) {}

  @Post("list")
  async query(@Req() req: { payload: Payload }, @Body() request: QueryRequest) {
    let status: "PROCESSED" | "UNPROCESSED" | "PROCESSING" | undefined;
    let node;
    if (
      Array.isArray(request.params?.node) &&
      request.params?.node?.length > 0
    ) {
      node = [...request.params.node];
      request.params.node = [];
    }
    if (!!request.params?.processStatus?.length) {
      status = request.params.processStatus[0];
      request.params.processStatus = "";
    }
    const { builder, pageable } = this.service.transfer(request);
    const subQuery = builder
      .subQuery()
      .select("1")
      .from(ReasonConfig, "prc")
      .innerJoin("prc.reason", "pr")
      .where(
        "prc.state in (:...states) and pr.stateIdx = prc.stateIdx and prc.ownerId = :ownerId",
        {
          ownerId: req.payload.id,
          states: [NodeState.ANALYZE, NodeState.VALIDATE],
        },
      )
      .andWhere("pr.delete is false")
      .andWhere(`pr.problem.id = ${builder.alias}.id`);
    if (!!node?.length) {
      subQuery.andWhere("pr.state in (:...node)", { node });
    }
    /* if (status === "UNPROCESSED") {
      subQuery.andWhere("prc.stateIdx < pr.stateIdx");
    } else if (status === "PROCESSED") {
      subQuery.andWhere("prc.stateIdx > pr.stateIdx");
    } else if (status === "PROCESSING") {
      subQuery.andWhere("prc.stateIdx = pr.stateIdx");
    } */
    builder.andWhereExists(subQuery);
    builder.andWhere(`${builder.alias}.status not in (:...statuses)`, {
      statuses: [
        ProblemStatus.DRAFT,
        ProblemStatus.OBSOLETE,
        ProblemStatus.CLOSED,
      ],
    });
    builder.addOrderBy(`${builder.alias}.id`, "DESC");
    return await this.service.doQuery(builder, pageable);
  }

  hasNoPermission(reason: Reason, currentUserId: number) {
    const config = reason.configs.find(config => config.state === reason.state);
    return !config || config.ownerId !== currentUserId;
  }

  @Get(":id/reason")
  async reasons(
    @Req() req: { payload: Payload },
    @Param("id") id: number,
  ): Promise<Reason[]> {
    const reasons = await this.reasonService.findByProblemId(id);
    return reasons.filter(
      item =>
        !item.delete &&
        item.configs.some(
          config =>
            config.ownerId === req.payload.id &&
            (config.state === NodeState.ANALYZE ||
              config.state === NodeState.VALIDATE),
        ),
    );
  }

  @Put(":id/reason")
  async updateReason(
    @Req() req: { payload: Payload },
    @Param("id") id: number,
    @Body() entity: Reason,
  ): Promise<Reason> {
    const target = await this.reasonService.get(id, { configs: true });
    if (
      (target.state !== NodeState.ANALYZE &&
        target.state !== NodeState.VALIDATE) ||
      this.hasNoPermission(target, req.payload.id)
    ) {
      throw new ForbiddenException("当前用户不能操作执行该操作");
    }
    if (target.state === NodeState.ANALYZE) {
      target.improvement = entity.improvement;
      target.unqualityType = entity.unqualityType;
      target.unqualityCode = entity.unqualityCode;
      target.estimatedFinishOn = entity.estimatedFinishOn;
    } else if (target.state === NodeState.VALIDATE) {
      target.validateResult = entity.validateResult;
    }
    return this.reasonService.execute(manager =>
      manager.save(this.reasonService.target, target),
    );
  }

  @Post(":id/transfer")
  async transferReason(
    @Req() req: { payload: Payload },
    @Param("id") id: number,
    @Body() entity: Reason,
  ): Promise<Reason> {
    const target = await this.reasonService.get(entity.id, { configs: true });
    if (
      (target.state !== NodeState.ANALYZE &&
        target.state !== NodeState.VALIDATE) ||
      this.hasNoPermission(target, req.payload.id)
    ) {
      throw new ForbiddenException("当前用户不能操作执行该操作");
    }
    const result = await this.reasonService.execute(async manager => {
      if (target.state === NodeState.ANALYZE) {
        target.configs = [
          ...entity.configs.map(config => ({
            ...config,
            stateIdx:
              nodes.find(node => node.state === config.state)?.index ?? -1,
          })),
          ...target.configs.filter(
            config =>
              config.state !== NodeState.ANALYZE &&
              config.state !== NodeState.ANALYZE_AUDIT &&
              config.copy !== "ANALYZE",
          ),
        ];
      } else if (target.state === NodeState.VALIDATE) {
        target.configs = [
          ...target.configs.filter(
            config =>
              config.state !== NodeState.VALIDATE &&
              config.state !== NodeState.VALIDATE_AUDIT &&
              config.state !== NodeState.CQE_AUDIT &&
              config.copy !== "VALIDATE",
          ),
          ...entity.configs.map(config => ({
            ...config,
            stateIdx:
              nodes.find(node => node.state === config.state)?.index ?? -1,
          })),
        ];
      }

      return manager.save(this.reasonService.target, target);
    });
    const problem = await this.service.get(id);
    if (!!result.todoId) {
      await this.userService.processedTodo(req.payload.token, [result.todoId]);
    }
    const results = await this.userService.addTodo(
      req.payload.token,
      [result],
      problem,
      result.state,
    );
    if (results?.length) {
      await this.reasonService.execute(async manager => {
        return manager.save(this.reasonService.target, results);
      });
    }
    this.mailService.sendEmail([result], result.state, problem);
    return result;
  }

  @Get(":id")
  async get(@Param("id") id: number) {
    return this.service.get(id);
  }

  @Post(":id/reason-detail")
  @UseInterceptors(
    FileInterceptor("file", {
      storage: reasonStorage,
      fileFilter: fileNameEncodingFilter,
    }),
  )
  async saveOrUpdateReasonDetail(
    @Req() req: { payload: Payload },
    @Param("id") id: number,
    @Body() body: { entity: string },
    @UploadedFile() file: Express.Multer.File,
  ) {
    const reason = await this.reasonService.get(id, { configs: true });
    if (
      reason.status === ReasonStatus.CLOSED ||
      reason.status === ReasonStatus.FOLLOW
    ) {
      throw new ForbiddenException("流程正在进行中或已关闭,非法操作");
    }
    if (
      reason.state !== NodeState.ANALYZE ||
      this.hasNoPermission(reason, req.payload.id)
    ) {
      throw new ForbiddenException("当前用户不能操作执行该操作");
    }
    const entity = JSON.parse(body.entity) as ReasonDetail;
    if (!entity.id) {
      entity.reason = { id } as Reason;
    }
    if (!!file) {
      entity.attachment = {
        bucket: file.destination,
        extension: extname(file.originalname),
        filename: file.originalname,
        key: parse(file.filename).name,
        size: file.size,
        type: file.mimetype,
      };
    }
    return this.reasonDetailService.saveOrUpdate(entity);
  }

  @Get("attachment/:id/:key")
  async attachment(
    @Param("id") id: number,
    @Param("key") key: string,
    @Res() res: Response,
  ) {
    const { attachment } = await this.reasonDetailService.get(id);
    res.sendFile(
      join(attachment.bucket, `${attachment.key}${attachment.extension}`),
      {
        headers: {
          "Content-Disposition": `attachment; filename*=${encodeURIComponent(
            attachment.filename,
          )}`,
        },
      },
    );
  }

  @Public()
  @Get("download/:id")
  async download(@Param("id") id: number, @Res() res: Response) {
    const { attachment } = await this.reasonDetailService.get(id);
    res.sendFile(
      join(attachment.bucket, `${attachment.key}${attachment.extension}`),
      {
        headers: {
          "Content-Disposition": `attachment; filename*=${encodeURIComponent(
            attachment.filename,
          )}`,
        },
      },
    );
  }

  @Delete(":id/reason-detail")
  async deleteReasonDetail(
    @Req() req: { payload: Payload },
    @Param("id") id: number,
    @Body() request: BatchIdRequest,
  ) {
    const reason = await this.reasonService.get(id, { configs: true });
    if (
      reason.status === ReasonStatus.CLOSED ||
      reason.status === ReasonStatus.FOLLOW
    ) {
      throw new ForbiddenException("流程正在进行中或已关闭,非法操作");
    }
    if (
      reason.state !== NodeState.ANALYZE ||
      this.hasNoPermission(reason, req.payload.id)
    ) {
      throw new ForbiddenException("当前用户不能操作执行该操作");
    }
    const details = await this.reasonDetailService.findByReasonId(id);
    const attachments: Attachment[] = [];
    const detailIds = details
      .filter(entity => request.ids.indexOf(entity.id) > -1)
      .map(entity => {
        if (!!entity.attachment) {
          attachments.push(entity.attachment);
        }
        return entity.id;
      });
    await this.reasonDetailService.delete(detailIds);

    for (const attachment of attachments) {
      try {
        unlinkSync(
          join(attachment.bucket, `${attachment.key}${attachment.extension}`),
        );
      } catch {}
    }
  }

  @Post("submit/:id")
  async submit(
    @Req() req: { payload: Payload },
    @Param("id") id: number,
    @Body() request?: { approved: boolean; remark?: string },
  ) {
    if (typeof request?.approved !== "undefined") {
      const reason = await this.reasonService.get(id, {
        problem: true,
        configs: true,
      });
      if (
        reason.state !== NodeState.VALIDATE ||
        this.hasNoPermission(reason, req.payload.id)
      ) {
        throw new ForbiddenException("当前用户不能操作执行该操作");
      }
      let log;
      if (!request.approved) {
        log = this.logService.repository.create({
          problem: { id: reason.problem.id },
          operatorId: req.payload.id,
          operatorName: req.payload.name,
          action: {
            key: "problem.action.reject_analyze_by_validate",
            message: `驳回了原因分析和行动计划`,
            params: { reason: request.remark },
          },
        });
        reason.state = NodeState.ANALYZE;
        reason.stateIdx = 0;
        reason.status = ReasonStatus.REJECTED;
        reason.remark = request.remark;
      } else {
        const node = nodes.find(node => node.state === reason.state);
        if (!node.next) {
          throw new ForbiddenException("非法的流程节点");
        }
        log = this.logService.repository.create({
          problem: { id: reason.problem.id },
          operatorId: req.payload.id,
          operatorName: req.payload.name,
          action: {
            key: "problem.action.approve_validate",
            message: `批准了效果验证`,
            params: { reason: request.remark },
          },
        });
        reason.remark = null;
        reason.state = node.next;
        reason.stateIdx = nodes.find(item => item.state === node.next).index;
        reason.validateOn = new Date();
      }
      const result = await this.reasonService.execute(manager => {
        manager.save(this.logService.target, log);
        return manager.save(this.reasonService.target, reason);
      });
      const problem = await this.service.get(reason.problem.id);
      if (!!result.todoId) {
        await this.userService.processedTodo(
          req.payload.token,
          [result.todoId],
          request.approved ? "1" : "2",
        );
      }
      const results = await this.userService.addTodo(
        req.payload.token,
        [result],
        problem,
        result.state,
      );
      if (results?.length) {
        await this.reasonService.execute(async manager => {
          return manager.save(this.reasonService.target, results);
        });
      }

      this.mailService.sendEmail([result], result.state, problem);
      return result;
    } else {
      const reason = await this.reasonService.get(id, {
        problem: true,
        configs: true,
      });
      if (
        reason.state !== NodeState.ANALYZE &&
        this.hasNoPermission(reason, req.payload.id)
      ) {
        throw new ForbiddenException("当前用户不能操作执行该操作");
      }
      if (
        reason.status === ReasonStatus.OPEN ||
        reason.status === ReasonStatus.REJECTED
      ) {
        reason.status = ReasonStatus.FOLLOW;
      }
      const node = nodes.find(node => node.state === reason.state);
      if (!node.next) {
        throw new ForbiddenException("非法的流程节点");
      }
      reason.state = node.next;
      reason.stateIdx = nodes.find(item => item.state === node.next).index;

      const log = this.logService.repository.create({
        problem: { id: reason.problem.id },
        operatorId: req.payload.id,
        operatorName: req.payload.name,
        action:
          reason.state === NodeState.ANALYZE_AUDIT
            ? {
                key: "problem.action.submit_analyze",
                message: "提交了原因分析和行动计划",
              }
            : {
                key: "problem.action.submit_validate",
                message: "提交了效果验证",
              },
      });
      const problem = await this.service.get(reason.problem.id);
      const result = await this.reasonService.execute(manager => {
        manager.save(this.logService.target, log);
        if (problem.status === ProblemStatus.NEW) {
          problem.status = ProblemStatus.PROCESSING;
          manager.save(this.service.target, problem);
        }
        return manager.save(this.reasonService.target, reason);
      });
      if (!!result.todoId) {
        await this.userService.processedTodo(req.payload.token, [
          result.todoId,
        ]);
      }
      const results = await this.userService.addTodo(
        req.payload.token,
        [result],
        problem,
        result.state,
      );
      if (results?.length) {
        await this.reasonService.execute(async manager => {
          return manager.save(this.reasonService.target, results);
        });
      }
      this.mailService.sendEmail([result], result.state, problem);
      return result;
    }
  }
}
