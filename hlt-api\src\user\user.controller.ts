import { Controller, Get, Req } from "@nestjs/common";
import type { Payload } from "src/auth/jwt.strategy";
import { Department, User, UserService } from "./user.service";

@Controller("user")
export class UserController {
  constructor(private readonly service: UserService) {}
  @Get("list")
  async list(@Req() request: { payload: Payload }): Promise<User[]> {
    return this.service.getUserList(request.payload.token);
  }

  @Get("departments")
  async departments(
    @Req() request: { payload: Payload },
  ): Promise<Department[]> {
    return this.service.getDepartmentList(request.payload.token);
  }
}
