import { Body, Controller, Delete, Post } from "@nestjs/common";
import { CodeRuleService } from "./code-rule.service";
import type { BatchIdRequest, QueryRequest } from "../framework/service/Query";
import { CodeRule } from "./entities/code-rule.entity";

@Controller("code-rule")
export class CodeRuleController {
  constructor(private readonly service: CodeRuleService) {}

  @Post("list")
  async query(@Body() request: QueryRequest) {
    return await this.service.query(request);
  }

  @Post("/")
  async save(@Body() entity: Partial<CodeRule>) {
    return await this.service.saveOrUpdate(entity);
  }

  @Delete("/")
  async remove(@Body() request: BatchIdRequest) {
    await this.service.delete(request.ids);
  }

  @Post("duplicate")
  async duplicate(@Body() entity: Partial<CodeRule>) {
    return this.service.duplicate(entity);
  }
}
