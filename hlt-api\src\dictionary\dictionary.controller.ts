import {
  Body,
  Controller,
  ForbiddenException,
  Get,
  HttpStatus,
  Param,
  Post,
  Res,
  UploadedFile,
  UseInterceptors,
} from "@nestjs/common";
import { FileInterceptor } from "@nestjs/platform-express";
import type { Response } from "express";
import { memoryStorage } from "multer";
import { Public } from "src/auth/public.decorator";
import Exporter from "src/utils/excel/exportData";
import { ExcelEntityPopulator } from "src/utils/excel/importData";
import type { QueryRequest } from "../framework/service/Query";
import { DictionaryRelationService } from "./dictionary-relation.service";
import { DictionaryService } from "./dictionary.service";
import {
  Dictionary,
  DictionaryCategory,
  DictionaryCategoryOpions,
  DictionaryRelation,
} from "./entities/dictionary.entity";
import { RemoteDictionaryService } from "./remote-dictionary.service";
import { DictionaryOption, DictionarySyncRequest } from "./types";

@Controller("dictionary")
export class DictionaryController {
  constructor(
    private readonly service: DictionaryService,
    private readonly rmService: RemoteDictionaryService,
    private readonly drService: DictionaryRelationService,
  ) {}

  @Public()
  @Post("list")
  async query(@Body() request: QueryRequest) {
    const data = await this.service.query(request);
    let datum: Dictionary;
    if (Array.isArray(data)) {
      datum = data.find(item => item.category === DictionaryCategory.FACTORY);
    } else {
      datum = data.data.find(
        item => item.category === DictionaryCategory.FACTORY,
      );
    }
    if (!!datum) {
      const factories = await this.rmService.getFactories();
      datum.options = factories.map(factory => ({ name: factory.label }));
    }
    return data;
  }

  @Post("/")
  async save(@Body() entity: Partial<Dictionary>) {
    return await this.service.saveOrUpdate(entity);
  }

  @Post("options")
  async getOptions(@Body() request: { categories?: DictionaryCategory[] }) {
    const result = await this.service.getOptions(request.categories);
    if (result.hasOwnProperty(DictionaryCategory.FACTORY)) {
      const factories = await this.rmService.getFactories();
      result[DictionaryCategory.FACTORY] = factories.map(factory => ({
        name: factory.label,
      }));
    }
    return result;
  }

  @Public()
  @Get(":category/options")
  async getCategoryOptions(@Param("category") category: DictionaryCategory) {
    if (category === DictionaryCategory.FACTORY) {
      const factories = await this.rmService.getFactories();
      return factories.map(factory => ({ name: factory.label }));
    }
    return await this.service.getCategoryOptions(category);
  }

  @Get(":category/template")
  async getCategoryTemplate(
    @Param("category") category: DictionaryCategory,
    @Res() res: Response,
  ) {
    if (category === DictionaryCategory.FACTORY) {
      throw new ForbiddenException("工厂字典不支持导入");
    }
    const dictName = DictionaryCategoryOpions[category];
    const options = await this.service.getCategoryOptions(category);
    const exporter = new Exporter();
    let data = [];
    let header = [];
    if (category === DictionaryCategory.REASON_TYPE) {
      for (const option of options) {
        if (!!option.children?.length) {
          for (const child of option.children) {
            data.push({ name: option.name, childName: child.name });
          }
        } else {
          data.push({ name: option.name, childName: "" });
        }
      }
      header = [
        { header: `一级${dictName}`, key: "name" },
        { header: `二级${dictName}`, key: "childName" },
      ];
    } else {
      header = [{ header: `${dictName}`, key: "name" }];
      data = options;
    }
    exporter.addSheet({
      name: category,
      header,
      data,
    });
    exporter.doExport({
      filename: dictName + "字典导入模板.xlsx",
      response: res,
    });
  }

  @Post(":category/import")
  @UseInterceptors(
    FileInterceptor("file", {
      storage: memoryStorage(),
    }),
  )
  async importDict(
    @Param("category") category: DictionaryCategory,
    @UploadedFile() file: Express.Multer.File,
  ) {
    if (category === DictionaryCategory.FACTORY) {
      throw new ForbiddenException("工厂字典不支持导入");
    }
    const dictName = DictionaryCategoryOpions[category];
    const populator = new ExcelEntityPopulator(file.buffer);
    await populator.init();
    if (!populator.sheetNames?.length || populator.sheetNames[0] !== category) {
      throw new ForbiddenException("请使用正确的字典");
    }
    const dictionary = await this.service.getDictionaryByCategory(category);
    if (category === DictionaryCategory.REASON_TYPE) {
      const options = populator.doImport<{ name: string; childName: string }>(
        0,
        {
          [`一级${dictName}`]: "name",
          [`二级${dictName}`]: "childName",
        },
      );
      const values: Record<string, string[]> = {};
      for (const option of options) {
        if (!!option.name?.length && !!option.childName?.length) {
          values[option.name] = values[option.name] ?? [];
          if (!values[option.name].includes(option.childName)) {
            values[option.name].push(option.childName);
          }
        }
      }
      dictionary.options = Object.entries(values).map(([name, children]) => {
        return { name, children: children.map(item => ({ name: item })) };
      });
    } else {
      const options = populator.doImport<DictionaryOption>(0, {
        [dictName]: "name",
      });
      console.log(options);
      const values: string[] = [];
      for (const option of options) {
        if (
          !!option.name?.length &&
          !!option.name.trim().length &&
          !values.includes(option.name)
        ) {
          values.push(option.name);
        }
      }
      dictionary.options = values.map(name => ({ name }));
    }
    await this.service.saveOrUpdate(dictionary);
  }

  @Get("relations/list")
  async getMachineTypeRelations() {
    return await this.drService.query({});
  }

  @Get("relations")
  async getMachineTypeRelationTemplate(@Res() res: Response) {
    const exporter = new Exporter();
    const customers = await this.service.getCategoryOptions(
      DictionaryCategory.CUSTOMER,
    );
    const businessUnits = await this.service.getCategoryOptions(
      DictionaryCategory.BUSINESS_UNIT,
    );
    const machineTypes = await this.service.getCategoryOptions(
      DictionaryCategory.MACHINE_TYPE,
    );
    const rows = Math.max(
      machineTypes.length,
      Math.max(businessUnits.length, customers.length),
    );
    const relations = (await this.drService.query({})) as DictionaryRelation[];
    const sheet1 = exporter.addSheet({
      name: "Sheet1",
      header: [
        { header: "机型", key: "machineType" },
        { header: "客户", key: "customer" },
        { header: "事业部", key: "businessUnit" },
      ],
      data: relations,
    });
    const { dataValidations } = sheet1 as any;
    (dataValidations as any).add("A2:A5000", {
      type: "list",
      allowBlank: false,
      formulae: [`Sheet2!$A$1:$A$${machineTypes.length}`],
    });
    (dataValidations as any).add("B2:B5000", {
      type: "list",
      allowBlank: false,
      formulae: [`Sheet2!$B$1:$B$${customers.length}`],
    });
    (dataValidations as any).add("C2:C5000", {
      type: "list",
      allowBlank: false,
      formulae: [`Sheet2!$C$1:$C$${businessUnits.length}`],
    });
    const sheet2 = exporter.addSheet({
      name: "Sheet2",
    });
    for (let i = 0; i < rows; i++) {
      const customer = customers.length > i ? customers[i].name : undefined;
      const businessUnit =
        businessUnits.length > i ? businessUnits[i].name : undefined;
      const machineType =
        machineTypes.length > i ? machineTypes[i].name : undefined;
      sheet2.addRow([machineType, customer, businessUnit]);
    }
    exporter.doExport({
      filename: "机型客户事业部字典关联维护模板.xlsx",
      response: res,
    });
  }

  private genKey(datum: Partial<DictionaryRelation>) {
    return `${datum.machineType}$$$${datum.customer}$$$${datum.businessUnit}`;
  }

  @Post("relations")
  @UseInterceptors(
    FileInterceptor("file", {
      storage: memoryStorage(),
    }),
  )
  async importRelation(
    @UploadedFile() file: Express.Multer.File,
    @Res() res: Response,
  ) {
    const populator = new ExcelEntityPopulator(file.buffer);
    await populator.init();
    const sheet = populator.sheets[0];
    if (
      sheet.getCell("A1")?.value !== "机型" ||
      sheet.getCell("B1")?.value !== "客户" ||
      sheet.getCell("C1")?.value !== "事业部"
    ) {
      throw new ForbiddenException("请使用正确的字典");
    }

    const mtDict = await this.service.getDictionaryByCategory(
      DictionaryCategory.MACHINE_TYPE,
    );
    const ctDict = await this.service.getDictionaryByCategory(
      DictionaryCategory.CUSTOMER,
    );
    const buDict = await this.service.getDictionaryByCategory(
      DictionaryCategory.BUSINESS_UNIT,
    );
    const mtOptions = mtDict.options.map(item => item.name);
    const ctOptions = ctDict.options.map(item => item.name);
    const buOptions = buDict.options.map(item => item.name);
    const rows = populator.doImport<{
      machineType: string;
      customer: string;
      businessUnit: string;
    }>(
      0,
      {
        机型: "machineType",
        客户: "customer",
        事业部: "businessUnit",
      },
      [
        (row, cell, key) => {
          if (key === "machineType") {
            if (cell.value === null || !cell.value.toString().trim().length) {
              return "机型不可为空";
            }
            if (!mtOptions.includes(cell.value.toString().trim())) {
              return `机型[${cell.value.toString().trim()}]不在机型字典中`;
            }
          } else if (key === "customer") {
            if (cell.value === null || !cell.value.toString().trim().length) {
              return "客户不可为空";
            }
            if (!ctOptions.includes(cell.value.toString().trim())) {
              return `客户[${cell.value.toString().trim()}]不在客户字典中`;
            }
          } else if (key === "businessUnit") {
            if (cell.value === null || !cell.value.toString().trim().length) {
              return undefined;
            }
            if (!buOptions.includes(cell.value.toString().trim())) {
              return `事业部[${cell.value.toString().trim()}]不在事业部字典中`;
            }
          }
          return undefined;
        },
      ],
    );
    if (populator.hasError) {
      populator.outputErrors(file.originalname, res);
    } else {
      const data = (await this.drService.query({})) as DictionaryRelation[];
      const keyMap = new Map<string, number>();
      for (const datum of data) {
        keyMap.set(this.genKey(datum), datum.id);
      }
      const rowKeys = rows.map(row => this.genKey(row));
      const toRemoveIds = [];
      for (const [key, id] of keyMap.entries()) {
        if (!rowKeys.includes(key)) {
          toRemoveIds.push(id);
          keyMap.delete(key);
        }
      }
      const toSaveEntities: DictionaryRelation[] = [];
      for (const row of rows) {
        const key = this.genKey(row);
        if (!keyMap.has(key)) {
          const entity = new DictionaryRelation();
          entity.customer = row.customer;
          if (!!row.businessUnit?.length) {
            entity.businessUnit = row.businessUnit;
          }
          entity.machineType = row.machineType;
          toSaveEntities.push(entity);
        }
      }
      await this.drService.execute(async manager => {
        if (toRemoveIds.length) {
          await manager
            .createQueryBuilder()
            .delete()
            .from(this.drService.target, "entity")
            .whereInIds(toRemoveIds)
            .execute();
        }
        if (toSaveEntities.length) {
          await manager
            .createQueryBuilder()
            .insert()
            .into(this.drService.target)
            .values(toSaveEntities)
            .execute();
        }
        return new Promise(resolve => {
          resolve(true);
        });
      });
      res.json({ code: 0 }).status(HttpStatus.OK);
    }
  }

  @Public()
  @Post("sync")
  async dictSync(@Body() request: DictionarySyncRequest) {
    const data = (await this.drService.query({})) as DictionaryRelation[];
    const keyMap = new Map<string, number>();
    for (const datum of data) {
      keyMap.set(this.genKey(datum), datum.id);
    }
    const rowKeys = request.relations.map(row => this.genKey(row));
    const toRemoveIds = [];
    for (const [key, id] of keyMap.entries()) {
      if (!rowKeys.includes(key)) {
        toRemoveIds.push(id);
        keyMap.delete(key);
      }
    }
    const toSaveEntities: DictionaryRelation[] = [];
    for (const row of request.relations) {
      const key = this.genKey(row);
      if (!keyMap.has(key)) {
        const entity = new DictionaryRelation();
        entity.customer = row.customer;
        entity.businessUnit = row.businessUnit;
        entity.machineType = row.machineType;
        toSaveEntities.push(entity);
      }
    }
    await this.drService.execute(async manager => {
      if (request.machineTypes?.length) {
        await manager
          .createQueryBuilder()
          .update(this.service.target)
          .set({ options: request.machineTypes.forEach(name => ({ name })) })
          .where("category = :category", {
            category: DictionaryCategory.MACHINE_TYPE,
          })
          .execute();
      }
      if (request.customers?.length) {
        await manager
          .createQueryBuilder()
          .update(this.service.target)
          .set({ options: request.customers.forEach(name => ({ name })) })
          .where("category = :category", {
            category: DictionaryCategory.CUSTOMER,
          })
          .execute();
      }
      if (request.businessUnits?.length) {
        await manager
          .createQueryBuilder()
          .update(this.service.target)
          .set({ options: request.businessUnits.forEach(name => ({ name })) })
          .where("category = :category", {
            category: DictionaryCategory.BUSINESS_UNIT,
          })
          .execute();
      }
      if (toRemoveIds.length) {
        await manager
          .createQueryBuilder()
          .delete()
          .from(this.drService.target, "entity")
          .whereInIds(toRemoveIds)
          .execute();
      }
      if (toSaveEntities.length) {
        await manager
          .createQueryBuilder()
          .insert()
          .into(this.drService.target)
          .values(toSaveEntities)
          .execute();
      }
      return new Promise(resolve => {
        resolve(true);
      });
    });
  }
}
