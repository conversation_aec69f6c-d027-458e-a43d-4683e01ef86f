import { Injectable } from "@nestjs/common";
import { BaseService } from "../framework/service/base.service";
import { InjectRepository } from "@nestjs/typeorm";
import { DataSource, Repository } from "typeorm";
import { ProblemOperateLog } from "./entities/problem-operate-log.entity";

@Injectable()
export class ProblemOperateLogService extends BaseService<ProblemOperateLog> {
  constructor(
    @InjectRepository(ProblemOperateLog)
    repository: Repository<ProblemOperateLog>,
    dataSource: DataSource,
  ) {
    super(repository, dataSource);
  }

  findByProblemId(id: number): Promise<ProblemOperateLog[]> {
    return this.repository.find({
      where: { problem: { id } },
      order: { createdAt: "ASC" },
      relations: { problem: true, reason: true },
    });
  }
}
