import { Column, Entity, ManyToOne } from "typeorm";
import { NumberIdObject } from "../../framework/model/base";
import { NodeState } from "./constant";
import { Reason } from "./reason.entity";

@Entity()
export class ReasonConfig extends NumberIdObject {
  @ManyToOne(() => Reason, reason => reason.configs, {
    nullable: true,
    onDelete: "CASCADE",
  })
  reason: Reason;
  @Column({ comment: "人员ID" })
  ownerId: number;
  @Column({ comment: "人员姓名" })
  ownerName: string;
  @Column({ nullable: true, comment: "人员邮箱" })
  ownerEmail: string;
  @Column({ nullable: true, comment: "部门" })
  ownerDepartment: string;
  @Column({
    type: "enum",
    enum: NodeState,
    nullable: true,
    comment: "节点",
  })
  state: NodeState;
  @Column({ nullable: true })
  stateIdx: number;
  @Column({
    type: "enum",
    nullable: true,
    enum: ["ANALYZE", "VALIDATE"],
    comment: "抄送人员节点",
  })
  copy: "ANALYZE" | "VALIDATE";
}
