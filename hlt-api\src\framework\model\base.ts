import {
  CreateDate<PERSON><PERSON>umn,
  PrimaryGenerated<PERSON><PERSON>umn,
  UpdateDateColumn,
} from "typeorm";

export type IDType = string | number;

export interface IdEntity<ID extends IDType> {
  id: ID;
}

export interface NamedEntity<ID extends IDType> extends IdEntity<ID> {
  name: string;
}

export interface CodedEntity<ID extends IDType> extends IdEntity<ID> {
  code: string;
}

export abstract class NumberIdObject implements IdEntity<number> {
  @PrimaryGeneratedColumn()
  id: number;
}

export abstract class NumberIdTimeObject extends NumberIdObject {
  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;
}

export type Result<D = any> = {
  code: string;
  message: string;
  data: D;
};

/*

export type PartEntity<T extends BaseEntity, K extends keyof T> = BaseEntity &
  Pick<T, K> &
  Partial<Omit<T, "id" | K>>;
*/

export type Attachment = {
  bucket: string;
  extension: string;
  filename: string;
  key: string;
  size: number;
  type: string;
};

export type ProblemDescription = {
  what: string;
  why: string;
  where: string;
  when: string;
  who: string;
  how_detected: string;
  how_many: string;
};
