datasource:
  - mysql:
      host: *************
      port: 3306
      username: yj
      password: Yjdi787^&d
      database: hertai-dfx
      logging: ["query", "error", "schema"]
      synchronize: false

#storage:
#  base:
#    dir: /home/<USER>/下载/test

server:
  url: http://*************:3001
  extUrl: http://*************:39985
  contextPath: admin-api
  loginPage: http://*************:3001/#/login?appCode=dfx
  carUrl: http://*************:3009/
#  mailTemplate: /home/<USER>/
  testCopyers: ["<EMAIL>"]
  name: DFX

port: 3001
contextPath: /api

mail:
  transport:
    host: smtp.qq.com  
    secure: true
    port: 465
    logger: true
    auth:
      user: <EMAIL>
      pass: jqhvfslwlcfgbffi
  defaults:
    from: '"No Reply" <<EMAIL>>' 

puppeteer:
  executablePath: #chrome可执行文件的绝对路径