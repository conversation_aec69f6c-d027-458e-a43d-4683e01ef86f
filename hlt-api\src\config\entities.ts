import { CodeRule } from "../code-rule/entities/code-rule.entity";
import { Dictionary } from "../dictionary/entities/dictionary.entity";
import { ProblemOperateLog } from "../problem/entities/problem-operate-log.entity";
import { Problem } from "../problem/entities/problem.entity";
import { ReasonConfig } from "../problem/entities/reason-config.entity";
import { ReasonDetail } from "../problem/entities/reason-detail.entity";
import { Reason } from "../problem/entities/reason.entity";
export default [
  CodeRule,
  Dictionary,
  ProblemOperateLog,
  Problem,
  ReasonConfig,
  ReasonDetail,
  Reason,
];
