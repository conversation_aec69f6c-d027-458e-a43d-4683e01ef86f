import { Modu<PERSON> } from "@nestjs/common";
import { CodeRuleService } from "./code-rule.service";
import { CodeRuleController } from "./code-rule.controller";
import { TypeOrmModule } from "@nestjs/typeorm";
import { CodeRule } from "./entities/code-rule.entity";

@Module({
  imports: [TypeOrmModule.forFeature([CodeRule])],
  controllers: [CodeRuleController],
  providers: [CodeRuleService],
  exports: [CodeRuleService],
})
export class CodeRuleModule {}
