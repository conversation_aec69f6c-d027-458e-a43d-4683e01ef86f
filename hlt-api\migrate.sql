ALTER TABLE `dictionary` 
MODIFY COLUMN `category` enum('CUSTOMER', 'BUSINESS_UNIT', 'FACTORY', 'PRODUCT_LINE', 'MACHINE_TYPE', 'PRODUCT_STEP', 'UNQUALITY_TYPE', 'REASON_TYPE', 'UNQUALITY_CODE') CHARACTER SET utf8mb4 COLLATE utf8mb4_bin NOT NULL DEFAULT 'CUSTOMER' AFTER `description`;

INSERT INTO `dictionary` (`name`, `description`, `category`, `priority`, `tree`, `options`) VALUES ('不良代码', '定义不良代码', 'UNQUALITY_CODE', 0, 0,'[]');
INSERT INTO `dictionary` (`name`, `description`, `category`, `priority`, `tree`, `options`) VALUES ('机种', '定义机种', 'MACHINE_CATEGORY', 0, 0,'[]');