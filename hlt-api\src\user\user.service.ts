import { Injectable } from "@nestjs/common";
import { BaseRemoteService } from "src/auth/base-remote.service";
import { NodeState, ProblemStatus } from "src/problem/entities/constant";
import { Problem } from "src/problem/entities/problem.entity";
import { Reason } from "src/problem/entities/reason.entity";
import { config } from "src/utils/properties";

export type User = {
  id: number;
  username: string;
  sub: number;
  password?: string;
  isDelete: number;
  jobNum: string;
  name: string;
  avatar?: string;
  tel?: string;
  email?: string;
  status?: string;
  auditStatus: string;
  reason?: string;
  postId: number;
  deptId: number;
  roleId: string[];
  rank: any;
  leader: number;
  indirectLeader?: number;
  languageCode?: string;
  postName: string;
};

export type Department = {
  id: number;
  parentId?: number;
  sort: number;
  name: string;
  leader: string;
};

export type Route = {
  id: number;
  parentId?: number;
  path: string;
  name: string;
  component: string;
  sort: number;
  meta: Record<string, any>;
  title: string;
};

export type Permission = {
  routes: Route[];
  buttons: string[];
};

export const MockPermission = {
  routes: [
    {
      id: 104,
      parentId: null,
      path: "/dashboard",
      name: "dashboard",
      component: null,
      sort: 1,
      meta: {
        isDelete: "0",
        createdBy: null,
        createdTime: "2024-01-18T03:07:49.173Z",
        updatedBy: null,
        updatedTime: "2024-01-18T03:07:49.173Z",
        workflowId: null,
        parentName: null,
        appCode: "car",
        perms: "dashboard",
        type: "M",
        redirect: null,
        icon: null,
        title: "首页",
        isLink: false,
        isHide: false,
        isFull: false,
        isAffix: false,
        isKeepAlive: false,
        status: "1",
      },
      title: "首页",
    },
    {
      id: 105,
      parentId: null,
      path: "/problem",
      name: "problem",
      component: null,
      sort: 1,
      meta: {
        isDelete: "0",
        createdBy: null,
        createdTime: "2024-01-18T03:09:12.108Z",
        updatedBy: null,
        updatedTime: "2024-01-18T03:09:12.108Z",
        workflowId: null,
        parentName: null,
        appCode: "car",
        perms: "problem",
        type: "M",
        redirect: null,
        icon: null,
        title: "问题列表",
        isLink: false,
        isHide: false,
        isFull: false,
        isAffix: false,
        isKeepAlive: false,
        status: "1",
      },
      title: "问题列表",
    },
    {
      id: 110,
      parentId: null,
      path: "/task",
      name: "task",
      component: null,
      sort: 1,
      meta: {
        isDelete: "0",
        createdBy: null,
        createdTime: "2024-01-18T03:13:05.906Z",
        updatedBy: null,
        updatedTime: "2024-01-18T03:13:05.906Z",
        workflowId: null,
        parentName: null,
        appCode: "car",
        perms: "task",
        type: "M",
        redirect: null,
        icon: null,
        title: "我的任务",
        isLink: false,
        isHide: false,
        isFull: false,
        isAffix: false,
        isKeepAlive: false,
        status: "1",
      },
      title: "我的任务",
    },
    {
      id: 113,
      parentId: null,
      path: "/audit",
      name: "audit",
      component: null,
      sort: 1,
      meta: {
        isDelete: "0",
        createdBy: null,
        createdTime: "2024-01-18T03:15:44.021Z",
        updatedBy: null,
        updatedTime: "2024-01-18T03:15:44.021Z",
        workflowId: null,
        parentName: null,
        appCode: "car",
        perms: "audit",
        type: "M",
        redirect: null,
        icon: null,
        title: "我的审批",
        isLink: false,
        isHide: false,
        isFull: false,
        isAffix: false,
        isKeepAlive: false,
        status: "1",
      },
      title: "我的审批",
    },
    {
      id: 116,
      parentId: null,
      path: "/stats",
      name: "stats",
      component: null,
      sort: 1,
      meta: {
        isDelete: "0",
        createdBy: null,
        createdTime: "2024-01-18T03:17:23.262Z",
        updatedBy: null,
        updatedTime: "2024-01-18T03:17:23.262Z",
        workflowId: null,
        parentName: null,
        appCode: "car",
        perms: "stats",
        type: "M",
        redirect: null,
        icon: null,
        title: "统计报表",
        isLink: false,
        isHide: false,
        isFull: false,
        isAffix: false,
        isKeepAlive: false,
        status: "1",
      },
      title: "统计报表",
    },
    {
      id: 117,
      parentId: null,
      path: "/basic",
      name: "basic",
      component: null,
      sort: 1,
      meta: {
        isDelete: "0",
        createdBy: null,
        createdTime: "2024-01-18T03:19:02.226Z",
        updatedBy: null,
        updatedTime: "2024-01-18T03:19:02.226Z",
        workflowId: null,
        parentName: null,
        appCode: "car",
        perms: "basic",
        type: "M",
        redirect: null,
        icon: null,
        title: "基础数据",
        isLink: false,
        isHide: false,
        isFull: false,
        isAffix: false,
        isKeepAlive: false,
        status: "1",
      },
      title: "基础数据",
    },
    {
      id: 118,
      parentId: 117,
      path: "/dictionary",
      name: "dictionary",
      component: null,
      sort: 1,
      meta: {
        isDelete: "0",
        createdBy: null,
        createdTime: "2024-01-18T03:20:11.958Z",
        updatedBy: null,
        updatedTime: "2024-01-18T03:20:11.958Z",
        workflowId: null,
        parentName: null,
        appCode: "car",
        perms: "dictionary",
        type: "M",
        redirect: null,
        icon: null,
        title: "字典管理",
        isLink: false,
        isHide: false,
        isFull: false,
        isAffix: false,
        isKeepAlive: false,
        status: "1",
      },
      title: "字典管理",
    },
    {
      id: 120,
      parentId: 117,
      path: "/code-rule",
      name: "coderule",
      component: null,
      sort: 1,
      meta: {
        isDelete: "0",
        createdBy: null,
        createdTime: "2024-01-18T03:21:21.427Z",
        updatedBy: null,
        updatedTime: "2024-01-18T03:21:21.427Z",
        workflowId: null,
        parentName: null,
        appCode: "car",
        perms: "coderule",
        type: "M",
        redirect: null,
        icon: null,
        title: "编码规则",
        isLink: false,
        isHide: false,
        isFull: false,
        isAffix: false,
        isKeepAlive: false,
        status: "1",
      },
      title: "编码规则",
    },
  ],
  buttons: [
    "problem:add",
    "problem:edit",
    "problem:export",
    "problem:view",
    "problem:delete",
    "task:view",
    "task:process",
    "audit:view",
    "audit:process",
    "dictionary:edit",
    "coderule:add",
    "coderule:delete",
    "coderule:edit",
  ],
};

export const MockUserData = [
  {
    id: 1,
    username: "user1",
    name: "原因分析人(总监)",
    email: "<EMAIL>",
    deptId: 11,
    leader: 1,
    indirectLeader: null,
  },
  {
    id: 2,
    username: "user2",
    name: "原因分析人(leader)",
    email: "<EMAIL>",
    deptId: 11,
    leader: 1,
    languageCode: "en",
    indirectLeader: null,
  },
  {
    id: 3,
    username: "user3",
    name: "原因分析人",
    email: "<EMAIL>",
    deptId: 132,
    leader: 2,
    roleId: ["1"],
    languageCode: "zh",
    indirectLeader: 1,
  },
  {
    id: 4,
    username: "user4",
    name: "效果验证人(总监)",
    email: "<EMAIL>",
    deptId: 2,
    leader: 4,
    indirectLeader: null,
  },
  {
    id: 5,
    username: "user5",
    name: "效果验证人(leader)",
    email: "<EMAIL>",
    deptId: 21,
    leader: 4,
    indirectLeader: null,
  },
  {
    id: 6,
    username: "user6",
    name: "效果验证人(有Leader)",
    email: "<EMAIL>",
    deptId: 21,
    leader: 5,
    indirectLeader: 4,
  },
  {
    id: 7,
    username: "user7",
    name: "效果验证人(无Leader)",
    email: "<EMAIL>",
    deptId: 22,
    leader: null,
    indirectLeader: null,
  },
  {
    id: 8,
    username: "user8",
    name: "原因分析人(无Leader)",
    email: "<EMAIL>",
    deptId: 231,
    leader: null,
    indirectLeader: null,
  },
  {
    id: 9,
    username: "CQE",
    name: "CQE",
    email: "<EMAIL>",
    deptId: 11,
    leader: 10,
    indirectLeader: null,
  },
  {
    id: 10,
    username: "CQE_LEADER",
    name: "CQE LEADER",
    email: "<EMAIL>",
    deptId: 11,
    leader: null,
    indirectLeader: null,
  },
] as User[];

export const MockDepartmentData = [
  { id: 1, parentId: null, sort: 0, name: "上海分公司", leader: null },
  { id: 11, parentId: 1, sort: 0, name: "市场部", leader: null },
  { id: 12, parentId: 1, sort: 1, name: "设计部", leader: null },
  { id: 13, parentId: 1, sort: 2, name: "生产部", leader: null },
  { id: 131, parentId: 13, sort: 0, name: "车间1", leader: null },
  { id: 132, parentId: 13, sort: 1, name: "车间2", leader: null },
  { id: 2, parentId: null, sort: 0, name: "北京分公司", leader: null },
  { id: 21, parentId: 2, sort: 0, name: "人事部", leader: null },
  { id: 22, parentId: 2, sort: 1, name: "销售部", leader: null },
  { id: 23, parentId: 2, sort: 2, name: "研发部", leader: null },
  { id: 231, parentId: 23, sort: 0, name: "设计组", leader: null },
  { id: 232, parentId: 23, sort: 1, name: "前端开发", leader: null },
];

@Injectable()
export class UserService extends BaseRemoteService {
  async findByToken(token: string): Promise<User | null> {
    if (token.length <= 2 && !isNaN(Number(token))) {
      return MockUserData.find(item => item.id === Number(token));
    }
    return await this.execute("/auth/profile", "GET", token);
  }

  async getUserList(token: string): Promise<User[]> {
    if (token.length <= 2 && !isNaN(Number(token))) {
      return MockUserData;
    }
    const { list } = await this.execute<{ list: User[] }>(
      "/sys_staff/list",
      "GET",
      token,
    );
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    return list.map(({ password, ...rest }) => ({ ...rest }));
  }

  async getDepartmentList(token: string): Promise<Department[]> {
    if (token.length <= 2 && !isNaN(Number(token))) {
      return MockDepartmentData;
    }
    const list = await this.execute<Department[]>(
      "/sys_dept/list",
      "GET",
      token,
    );
    return list.map(({ id, parentId, sort, name, leader }) => ({
      id,
      parentId,
      sort,
      name,
      leader,
    }));
  }

  async getPermissions(token: string): Promise<Permission> {
    if (token.length <= 2 && !isNaN(Number(token))) {
      return MockPermission;
    }
    const params = new URLSearchParams({
      appCode: config.appName.toLowerCase(),
    });
    return await this.execute<Permission>(
      "/sys_role/auth?" + params,
      "GET",
      token,
    );
  }

  async getLanguages(): Promise<
    { code: string; langData: { label: string; value: string }[] }[]
  > {
    return await this.execute("/language_public/code/listAll", "GET");
  }

  async addTodo(
    token: string,
    reasons: Reason[],
    problem: Problem,
    state: NodeState,
  ): Promise<Reason[]> {
    if (token.length <= 2 && !isNaN(Number(token))) {
      return;
    }
    const results = [];
    if (problem.status === ProblemStatus.CQE && !!problem.cqeId) {
      const redirect = encodeURIComponent(`/problem`);
      const url = `${config.loginPage}&redirect=${redirect}`;
      const res = await this.execute<Record<string, any>>(
        "/sys_todo",
        "POST",
        token,
        {
          status: "0",
          userId: problem.cqeId,
          title: `QMS通知：【${config.appName}】${problem.code}待分配`,
          url,
          appCode: config.appName.toLowerCase(),
          path: "",
          menuId: -1,
        },
      );
      if (!!res) {
        problem.todoId = res.id;
      }
    } else if (state === NodeState.ANALYZE || state === NodeState.VALIDATE) {
      const redirect = encodeURIComponent(`/task?entityId=${problem.id}`);
      const url = `${config.loginPage}&redirect=${redirect}`;
      for (const reason of reasons) {
        const _config = reason.configs.find(
          nodeConfig => nodeConfig.state === state && !!nodeConfig.ownerId,
        );
        if (!_config) {
          continue;
        }
        const res = await this.execute<Record<string, any>>(
          "/sys_todo",
          "POST",
          token,
          {
            status: "0",
            userId: _config.ownerId,
            title: `QMS通知：【${config.appName}】${problem.code}待处理`,
            url,
            appCode: config.appName.toLowerCase(),
            path: "",
            menuId: -1,
          },
        );
        if (!!res) {
          reason.todoId = res.id;
        }
        results.push(reason);
      }
    } else if (
      state === NodeState.ANALYZE_AUDIT ||
      state === NodeState.VALIDATE_AUDIT ||
      state === NodeState.CQE_AUDIT
    ) {
      const redirect = encodeURIComponent(`/audit?entityId=${problem.id}`);
      const url = `${config.loginPage}&redirect=${redirect}`;
      for (const reason of reasons) {
        const _config = reason.configs.find(
          nodeConfig => nodeConfig.state === state && !!nodeConfig.ownerId,
        );
        if (!_config) {
          continue;
        }
        const res = await this.execute<Record<string, any>>(
          "/sys_todo",
          "POST",
          token,
          {
            status: "0",
            userId: _config.ownerId,
            title: `QMS通知：【${config.appName}】${problem.code}待审批`,
            url,
            appCode: config.appName.toLowerCase(),
            path: "",
            menuId: -1,
          },
        );
        if (!!res) {
          reason.todoId = res.id;
        }
        results.push(reason);
      }
    }
    return results;
  }

  async processedTodo(
    token: string,
    ids: number[],
    status = "1",
  ): Promise<void> {
    for (const id of ids) {
      await this.execute("/sys_todo", "PUT", token, {
        status,
        id,
      });
    }
  }
}
