export enum NodeState {
  ANALYZE = "ANALYZE",
  ANALYZE_AUDIT = "ANALYZE_AUDIT",
  VALIDATE = "VALIDATE",
  VALIDATE_AUDIT = "VALIDATE_AUDIT",
  CQE_AUDIT = "CQE_AUDIT",
  COMPLETE = "COMPLETE",
}

export const PRIVATE_KEY = "F5934C14C0F0193CA1D0344AF9C99739";

export type Node = {
  index: number;
  state: NodeState;
  next?: NodeState;
};

export const nodes: Node[] = [
  { index: 0, state: NodeState.ANALYZE, next: NodeState.ANALYZE_AUDIT },
  { index: 1, state: NodeState.ANALYZE_AUDIT, next: NodeState.VALIDATE },
  { index: 2, state: NodeState.VALIDATE, next: NodeState.VALIDATE_AUDIT },
  { index: 3, state: NodeState.VALIDATE_AUDIT, next: NodeState.CQE_AUDIT },
  { index: 4, state: NodeState.CQE_AUDIT, next: NodeState.COMPLETE },
  { index: 5, state: NodeState.COMPLETE },
];

export enum ProblemStatus {
  DRAFT = "DRAFT",
  CQE = "CQE",
  NEW = "NEW",
  PROCESSING = "PROCESSING",
  CLOSED = "CLOSED",
  OBSOLETE = "OBSOLETE",
}

export enum ReasonDetailType {
  PRODUCE = "PRODUCE",
  EXPOSE = "EXPOSE",
  SYSTEM = "SYSTEM",
}

export enum ReasonStatus {
  OPEN = "OPEN",
  FOLLOW = "FOLLOW",
  CLOSED = "CLOSED",
  REJECTED = "REJECTED",
}
