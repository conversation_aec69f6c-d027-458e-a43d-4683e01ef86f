{"name": "hlt-api", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "bin": "dist/main.js", "pkg": {"scripts": "dist/**/*.js", "assets": ["node_modules/swagger-ui-dist", "node_modules/swagger-ui-express", "node_modules/es-get-iterator/**/*.*", "node_modules/pdfkit/**/*.*", "dist/mail/templates/*.hbs", "dist/migrations"]}, "scripts": {"pkg:win": "pkg . -t node18-win-x64 -o target/api-win --debug", "pkg:mac": "pkg . -t node18-mac-x64 -o target/api-mac --debug", "pkg:linux": "pkg . -t node18-linux-x64 -o target/api-linux", "build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "cross-env NODE_ENV=development nest start", "start:dev": "cross-env NODE_ENV=development nest start --watch", "start:dfx": "cross-env NODE_ENV=development APP=DFX nest start --watch", "start:hmr": "cross-env NODE_ENV=development nest build --webpack --webpackPath webpack-hmr.config.js --watch", "start:debug": "cross-env NODE_ENV=development nest start --debug --watch", "start:prod": "cross-env NODE_ENV=production node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json"}, "dependencies": {"@nestjs-modules/mailer": "^1.10.3", "@nestjs/common": "^10.3.0", "@nestjs/core": "^10.3.0", "@nestjs/jwt": "^10.2.0", "@nestjs/mapped-types": "^2.0.4", "@nestjs/passport": "^10.0.3", "@nestjs/platform-express": "^10.3.0", "@nestjs/platform-fastify": "^10.3.0", "@nestjs/schedule": "^4.0.1", "@nestjs/typeorm": "^10.0.1", "@types/pdfkit": "^0.13.4", "async-lock": "^1.4.1", "bcrypt": "^5.1.1", "crypto-js": "^4.2.0", "dayjs": "^1.11.10", "deep-equal": "^2.2.3", "exceljs": "^4.4.0", "express-async-errors": "^3.1.1", "handlebars": "^4.7.8", "multer": "1.4.5-lts.1", "mysql2": "^3.6.5", "node-fetch": "^2.7.0", "nodemailer": "^6.9.7", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "pdfkit": "^0.14.0", "properties-reader": "^2.3.0", "puppeteer-core": "^22.3.0", "reflect-metadata": "^0.1.14", "rxjs": "^7.8.1", "typeorm": "^0.3.17", "typeorm-naming-strategies": "^4.1.0", "uuid": "^9.0.1", "yaml": "^2.3.4"}, "devDependencies": {"@nestjs/cli": "^10.2.1", "@nestjs/schematics": "^10.0.3", "@nestjs/testing": "^10.3.0", "@swc/cli": "^0.1.63", "@swc/core": "^1.3.101", "@types/async-lock": "^1.4.2", "@types/crypto-js": "^4.2.1", "@types/express": "^4.17.21", "@types/jest": "^29.5.11", "@types/multer": "^1.4.11", "@types/node": "^20.10.5", "@types/node-fetch": "^2.6.9", "@types/nodemailer": "^6.4.14", "@types/passport-jwt": "^3.0.13", "@types/properties-reader": "^2.1.3", "@types/supertest": "^2.0.16", "@types/uuid": "^9.0.7", "cross-env": "^7.0.3", "eslint": "^8.56.0", "eslint-config-nestjs": "^0.7.4", "fork-ts-checker-webpack-plugin": "^9.0.2", "jest": "^29.7.0", "run-script-webpack-plugin": "^0.2.0", "source-map-support": "^0.5.21", "supertest": "^6.3.3", "ts-jest": "^29.1.1", "ts-loader": "^9.5.1", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.3.3", "webpack": "^5.89.0", "webpack-node-externals": "^3.0.0"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}