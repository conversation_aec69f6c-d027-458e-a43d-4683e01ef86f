import { Column, Entity } from "typeorm";
import { NamedEntity, NumberIdObject } from "../../framework/model/base";
import { DictionaryOption } from "../types";

export enum DictionaryCategory {
  CUSTOMER = "CUSTOMER",
  BUSINESS_UNIT = "BUSINESS_UNIT",
  FACTORY = "FACTORY",
  PRODUCT_LINE = "PRODUCT_LINE",
  MACHINE_TYPE = "MACHINE_TYPE",
  PRODUCT_STEP = "PRODUCT_STEP",
  UNQUALITY_TYPE = "UNQUALITY_TYPE",
  UNQUALITY_CODE = "UNQUALITY_CODE",
  REASON_TYPE = "REASON_TYPE",
}

export const DictionaryCategoryOpions: Record<DictionaryCategory, string> = {
  [DictionaryCategory.CUSTOMER]: "客户",
  [DictionaryCategory.BUSINESS_UNIT]: "事业部",
  [DictionaryCategory.FACTORY]: "工厂",
  [DictionaryCategory.PRODUCT_LINE]: "生产线",
  [DictionaryCategory.MACHINE_TYPE]: "机型",
  [DictionaryCategory.PRODUCT_STEP]: "产品阶段",
  [DictionaryCategory.UNQUALITY_TYPE]: "不良类别",
  [DictionaryCategory.UNQUALITY_CODE]: "不良代码",
  [DictionaryCategory.REASON_TYPE]: "原因类别",
};

@Entity()
export class Dictionary extends NumberIdObject implements NamedEntity<number> {
  @Column()
  name: string;

  @Column()
  description?: string;

  @Column({
    type: "enum",
    enum: DictionaryCategory,
    default: DictionaryCategory.CUSTOMER,
  })
  category: DictionaryCategory;

  @Column("json")
  options: DictionaryOption[];

  @Column()
  priority: number;

  @Column({ default: false })
  tree: boolean;
}

@Entity()
export class DictionaryRelation extends NumberIdObject {
  @Column()
  machineType: string;
  @Column({ nullable: true })
  customer: string;
  @Column({ nullable: true })
  businessUnit: string;
}
