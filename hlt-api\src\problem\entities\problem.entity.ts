import { Column, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Relation } from "typeorm";
import {
  Attachment,
  CodedEntity,
  NumberIdTimeObject,
  ProblemDescription,
} from "../../framework/model/base";
import { ProblemStatus } from "./constant";
import { ProblemOperateLog } from "./problem-operate-log.entity";
import { Reason } from "./reason.entity";

@Entity()
export class Problem extends NumberIdTimeObject implements CodedEntity<number> {
  @Column({ nullable: true, length: 100 })
  code: string;
  @Column({ nullable: true, type: "date" })
  createdOn: Date;
  @Column({ nullable: true, comment: "机种" })
  machineCategory: string;
  @Column({ nullable: true, comment: "机型" })
  machineType: string;
  @Column({ nullable: true, comment: "客户" })
  customer: string;
  @Column({ nullable: true, comment: "项目编号" })
  projectCode: string;
  @Column({ nullable: true, comment: "生产线" })
  productLine: string;
  @Column({ nullable: true, comment: "工厂" })
  factory: string;
  @Column({ nullable: true, comment: "事业部" })
  businessUnit: string;
  @Column({ nullable: true, comment: "创建人ID" })
  creatorId: number;
  @Column({ nullable: true, comment: "创建人姓名" })
  creatorName: string;
  @Column({ nullable: true, comment: "产品阶段" })
  productStep: string;
  @Column({
    type: "enum",
    enum: ProblemStatus,
    default: ProblemStatus.DRAFT,
    comment: "项目状态",
  })
  status: ProblemStatus;
  @Column({ nullable: true, comment: "问题描述", type: "text" })
  description: string;
  @Column({ nullable: true, comment: "问题描述(5w2h)", type: "json" })
  descriptions: ProblemDescription;
  @Column({ nullable: true, type: "json", comment: "好件附件" })
  goodPartImages: Attachment[];
  @Column({ nullable: true, type: "json", comment: "坏件附件" })
  badPartImages: Attachment[];

  @Column({ nullable: true, comment: "工单号" })
  workOrderCode: string;
  @Column({ nullable: true, comment: "工单数量" })
  workOrderNum: number;

  @OneToMany(() => Reason, reason => reason.problem, {
    cascade: true,
    orphanedRowAction: "delete",
  })
  reasons: Relation<Reason>[];

  @OneToMany(() => ProblemOperateLog, log => log.problem, {
    cascade: true,
    orphanedRowAction: "delete",
  })
  logs: Relation<ProblemOperateLog>[];

  @Column({ nullable: true, comment: "CQE ID" })
  cqeId: number;

  @Column({ nullable: true, comment: "CQE 姓名" })
  cqeName: string;

  @Column({ nullable: true, comment: "CQE TODO ID" })
  todoId: number;

  @Column({ nullable: true, comment: '其他系统编码' })
  extCode: string;

}
