import {
  DataSource,
  DeepPartial,
  FindOptionsRelationByString,
  FindOptionsRelations,
  Repository,
  SelectQueryBuilder,
} from "typeorm";
import { IBaseService, Page } from "./IBase.service";
import { NumberIdObject } from "../model/base";
import { Query, QueryRequest } from "./Query";
import { DeleteResult } from "typeorm/query-builder/result/DeleteResult";
import { FindOptionsWhere } from "typeorm/find-options/FindOptionsWhere";
import { EntityManager } from "typeorm/entity-manager/EntityManager";
import { InternalServerErrorException } from "@nestjs/common";

export abstract class BaseService<T extends NumberIdObject>
  implements IBaseService<T>
{
  protected constructor(
    private readonly _repository: Repository<T>,
    private dataSource: DataSource,
  ) {}

  get repository() {
    return this._repository;
  }

  get target() {
    return this.repository.target;
  }

  async query(request: QueryRequest): Promise<T[] | Page<T>> {
    const query = new Query<T>(request);
    if (query.pageable) {
      const [data, total] = await this.repository.findAndCount(query);
      return {
        data: this.mapperMany(data),
        total,
      };
    }
    return this.mapperMany(await this.repository.find(query));
  }

  transfer(
    request: QueryRequest,
    alias?: string,
  ): {
    builder: SelectQueryBuilder<T>;
    pageable: boolean;
  } {
    const query = new Query<T>(request);
    const builder = this.repository.createQueryBuilder(alias);
    if (!!query.where) {
      builder.andWhere(query.where);
    }
    if (!!query.order) {
      for (const key of Object.keys(query.order)) {
        builder.addOrderBy(`${builder.alias}.${key}`, query.order[key]);
      }
    }
    if (!!query.take) {
      builder.take(query.take);
    }
    if (!!query.skip) {
      builder.skip(query.skip);
    }
    return { builder, pageable: query.pageable };
  }

  populateQueryBuilder(builder: SelectQueryBuilder<T>) {}

  async doQuery(
    builder: SelectQueryBuilder<T>,
    pageable: boolean,
  ): Promise<T[] | Page<T>> {
    this.populateQueryBuilder(builder);
    const data = this.mapperMany(await builder.getMany());
    if (pageable) {
      const total = await builder.getCount();
      return {
        data,
        total,
      };
    }
    return data;
  }

  mapperMany(data: T[]): T[] {
    return data.map(datum => this.mapper(datum));
  }

  mapper(datum: T): T {
    return datum;
  }

  assignSkipFields(): string[] {
    return ["id", "createdAt", "updatedAt"];
  }

  processUpdate(source: Partial<T>, target: T) {
    const skipFields = this.assignSkipFields();
    for (const key of Object.keys(source)) {
      if (skipFields.includes(key)) {
        continue;
      }
      target[key] = source[key];
    }
  }

  async preSave(_entity: T) {}

  async saveOrUpdate(source: Partial<T>): Promise<T> {
    const entity = source.id
      ? await this.get(source.id)
      : this.repository.create(source as DeepPartial<T>);
    if (!!source.id) {
      this.processUpdate(source, entity);
    }
    return await this.execute(async manager => {
      await this.preSave(entity);
      return manager.save(this.target, entity);
    });
  }

  async execute<R>(func: (entityManager: EntityManager) => Promise<R>) {
    return await this.dataSource.transaction<R>(func);
  }

  async delete(ids: number[]): Promise<void> {
    await this.execute<DeleteResult>(manager =>
      manager
        .createQueryBuilder()
        .delete()
        .from(this.repository.target, "entity")
        .whereInIds(ids)
        .execute(),
    );
  }

  async get(
    id: number,
    relations?: FindOptionsRelations<T> | FindOptionsRelationByString,
  ): Promise<T> {
    const where = {
      id,
    } as FindOptionsWhere<T>;
    const entity = !!relations
      ? await this.repository.findOne({
          where,
          relations,
        })
      : await this.repository.findOneBy(where);
    if (entity === null) {
      throw new InternalServerErrorException("Entity not found");
    }
    return this.mapper(entity);
  }
}
