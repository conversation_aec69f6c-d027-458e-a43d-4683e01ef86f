import { Injectable } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { BaseService } from "src/framework/service/base.service";
import { DataSource, Repository } from "typeorm";
import { ReasonDetail } from "./entities/reason-detail.entity";

@Injectable()
export class ReasonDetailService extends BaseService<ReasonDetail> {
  constructor(
    @InjectRepository(ReasonDetail) repository: Repository<ReasonDetail>,
    dataSource: DataSource,
  ) {
    super(repository, dataSource);
  }

  assignSkipFields(): string[] {
    return [...super.assignSkipFields(), "reason", "type"];
  }

  findByReasonId(id: number): Promise<ReasonDetail[]> {
    return this.repository.find({
      where: { reason: { id } },
      order: { id: "ASC" },
    });
  }
}
