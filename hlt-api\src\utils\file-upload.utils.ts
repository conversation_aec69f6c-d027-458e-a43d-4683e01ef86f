import {
  HttpException,
  HttpStatus,
  InternalServerErrorException,
} from "@nestjs/common";

export class FileNotMatchException extends HttpException {
  constructor(msg: string = "File Type Not Match") {
    super(msg, HttpStatus.OK);
  }
}

export const imageFileFilter = (
  _req: Express.Request,
  file: Express.Multer.File,
  callback: CallableFunction,
) => {
  if (
    /^image\/.*/.test(file.mimetype) ||
    /\.(jpg|jpeg|png|gif|svg|tiff)$/.test(file.originalname)
  ) {
    return fileNameEncodingFilter(_req, file, callback);
  }
  return callback(new InternalServerErrorException("只允许图片文件"), false);
};

export const fileNameEncodingFilter = (
  _req: Express.Request,
  file: Express.Multer.File,
  callback: CallableFunction,
) => {
  if (!/[^\u0000-\u00ff]/.test(file.originalname)) {
    file.originalname = Buffer.from(file.originalname, "latin1").toString(
      "utf8",
    );
  }
  return callback(null, true);
};
