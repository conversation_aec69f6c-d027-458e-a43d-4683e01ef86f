import { NumberIdTimeObject } from "../../framework/model/base";
import { Column, <PERSON><PERSON><PERSON>, ManyToOne, Relation } from "typeorm";
import { Problem } from "./problem.entity";
import { Reason } from "./reason.entity";

@Entity()
export class ProblemOperateLog extends NumberIdTimeObject {
  @ManyToOne(() => Problem, problem => problem.logs, { onDelete: "CASCADE" })
  problem: Relation<Problem>;
  @ManyToOne(() => Reason, { nullable: true })
  reason?: Relation<Reason>;
  @Column({ comment: "操作人ID" })
  operatorId: number;
  @Column({ comment: "操作人姓名" })
  operatorName: string;
  @Column({ type: "json", comment: "操作" })
  action: { key: string; message: string; params: Record<string, string> };
  @Column({ nullable: true, type: "json" })
  before: Record<string, any>;
  @Column({ nullable: true, type: "json" })
  after: Record<string, any>;
}
