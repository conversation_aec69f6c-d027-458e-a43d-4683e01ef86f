import { Injectable, InternalServerErrorException } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { unlinkSync } from "fs";
import { join } from "path";
import { Page } from "src/framework/service/IBase.service";
import { QueryRequest } from "src/framework/service/Query";
import { BaseService } from "src/framework/service/base.service";
import { DataSource, In, Repository, SelectQueryBuilder } from "typeorm";
import { NodeState, ProblemStatus } from "./entities/constant";
import { ProblemOperateLog } from "./entities/problem-operate-log.entity";
import { Problem } from "./entities/problem.entity";
import { ReasonConfig } from "./entities/reason-config.entity";
import { Reason } from "./entities/reason.entity";

@Injectable()
export class ProblemService extends BaseService<Problem> {

  constructor(
    @InjectRepository(Problem) repository: Repository<Problem>,
    dataSource: DataSource,
    @InjectRepository(ProblemOperateLog)
    private logRepository: Repository<ProblemOperateLog>,
  ) {
    super(repository, dataSource);
  }

  populateQueryBuilder(builder: SelectQueryBuilder<Problem>): void {
    builder.leftJoinAndSelect(
      `${builder.alias}.reasons`,
      "r",
      "r.delete is false",
    );
    builder.leftJoinAndSelect(`r.configs`, "c");
  }

  async query(request: QueryRequest): Promise<Problem[] | Page<Problem>> {
    let owner, node, remark;
    if (Array.isArray(request.params.node) && request.params.node.length > 0) {
      node = [...request.params.node];
      request.params.node = [];
    }
    if (!!request.params.owner?.length) {
      owner = request.params.owner;
      request.params.owner = "";
    }
    if (!!request.params.remark?.length) {
      remark = request.params.remark;
      request.params.remark = "";
    }

    const { builder, pageable } = this.transfer(request);
    if (!!node?.length) {
      builder.andWhereExists(
        builder
          .subQuery()
          .select("1")
          .from(Reason, "pr")
          .where("pr.state in (:...node)", { node })
          .andWhere("pr.delete is false")
          .andWhere(`pr.problem.id = ${builder.alias}.id`),
      );
    }
    if (!!owner?.length) {
      builder.andWhereExists(
        builder
          .subQuery()
          .select("1")
          .from(ReasonConfig, "prc")
          .innerJoin("prc.reason", "pr")
          .where(
            `
            CASE WHEN pr.state in (:...states) THEN prc.state = pr.state and prc.ownerName like :owner 
                 WHEN pr.state = :state1 THEN 1=2 END
            `,
            {
              owner: `%${owner}%`,
              states: [
                NodeState.ANALYZE,
                NodeState.VALIDATE,
                NodeState.ANALYZE_AUDIT,
                NodeState.VALIDATE_AUDIT,
                NodeState.CQE_AUDIT,
              ],
              state1: NodeState.COMPLETE,
            },
          )
          .andWhere("pr.delete is false")
          .andWhere(`pr.problem.id = ${builder.alias}.id`),
      );
    }
    if (!!remark?.length) {
      builder.andWhereExists(
        builder
          .subQuery()
          .select("1")
          .from(Reason, "pr")
          .where("pr.remark like :remark", { remark: `%${remark}%` })
          .andWhere("pr.delete is false")
          .andWhere(`pr.problem.id = ${builder.alias}.id`),
      );
    }
    builder.addOrderBy(`${builder.alias}.id`, "DESC");
    return this.doQuery(builder, pageable);
    /*
   const query = new Query<Problem>(request);
     if (!!remark?.length) {
      query.where["reasons"] = query.where["reasons"] ?? {};
      query.where["reasons"] = {
        remark: ILike(`%${remark}%`),
      };
    }
    if (!!owner?.length) {
      query.where["reasons"] = query.where["reasons"] ?? {};
      query.where["reasons"] = {
        configs: {
          ownerName: ILike(`%${owner}%`),
        },
      };
    }
    if (!!node?.length) {
      query.where["reasons"] = query.where["reasons"] ?? {};
      query.where["reasons"] = {
        state: In(node),
      };
    }
    if (
      !((request.params.status as string[]) ?? []).includes(ProblemStatus.DRAFT)
    ) {
      query.relations = {
        reasons: { configs: true },
      };
    } 
    if (query.pageable) {
      const [data, total] = await this.repository.findAndCount(query);
      return {
        data: this.mapperMany(data),
        total,
      };
    }
    return this.mapperMany(await this.repository.find(query));
    */
  }
  assignSkipFields(): string[] {
    return [
      ...super.assignSkipFields(),
      "creatorId",
      "creatorName",
      "status",
      "goodPartImages",
      "badPartImages",
      "reasons",
      "logs",
    ];
  }

  async preSave(entity: Problem): Promise<void> {
    if (!entity.id) {
      const log = this.logRepository.create({
        operatorId: entity.creatorId,
        operatorName: entity.creatorName,
        action: { key: "problem.action.create", message: "创建了问题" },
      });
      entity.logs = [log];
    }
  }

  async deleteEntities(
    entities: Problem[],
    isAdmin: boolean,
    cb: (toObsolete: Problem[]) => Promise<void> = async () => { },
  ) {
    const toRemove: Problem[] = [];
    const toObsolete: Problem[] = [];
    entities.forEach(entity => {
      if (
        entity.status === ProblemStatus.DRAFT ||
        entity.status === ProblemStatus.CQE
      ) {
        toRemove.push(entity);
      } else if (
        isAdmin ||
        (entity.status !== ProblemStatus.CLOSED &&
          entity.status !== ProblemStatus.OBSOLETE)
      ) {
        toObsolete.push(entity);
      }
    });
    const attachments = toRemove.flatMap(entity =>
      (entity.goodPartImages ?? []).concat(entity.badPartImages ?? []),
    );
    await this.execute(async manager => {
      if (!!toObsolete.length) {
        const logs = [];
        toObsolete.forEach(item => {
          item.status = ProblemStatus.OBSOLETE;
          logs.push(
            this.logRepository.create({
              problem: { id: item.id },
              operatorId: item.creatorId,
              operatorName: item.creatorName,
              action: { key: "problem.action.obsolete", message: "作废了问题" },
            }),
          );
        });
        await cb(toObsolete);
        await manager.save(this.logRepository.target, logs);
        await manager.save(this.target, toObsolete);
      }
      if (!!toRemove.length) {
        await manager
          .createQueryBuilder()
          .delete()
          .from(this.target, "entity")
          .whereInIds(toRemove.map(item => item.id))
          .execute();
      }
    });
    for (const attachment of attachments) {
      unlinkSync(
        join(attachment.bucket, `${attachment.key}${attachment.extension}`),
      );
    }
  }

  async delete(ids: number[]): Promise<void> {
    const entities = await this.repository.findBy({ id: In(ids) });
    this.deleteEntities(entities, false);
  }


  async getByCode(code: string) {
    const entity = await this.repository.findOneBy({ code });
    if (entity === null) {
      throw new InternalServerErrorException("Entity not found");
    }
    return this.mapper(entity);
  }
}
